/**
 * API 注册与检测模块
 */
import { requestMethods } from './request';

// 动态导入API模块
const requireModule = require.context('./modules', false, /\.js$/);
const modules = requireModule.keys().map(file => requireModule(file).default);

// 合并所有API
export const apiList = modules.flat();

/**
 * 检查重复API名称
 */
export const detectDuplicateApis = () => {
	// 构建API名称与API对象的映射关系
	const urlNameMap = apiList.reduce((map, api) => {
		const { urlName } = api;
		const apis = map.get(urlName) || [];
		apis.push(api);
		map.set(urlName, apis);
		return map;
	}, new Map());

	// 过滤出重复的API并打印
	const duplicates = Array.from(urlNameMap.entries())
		.filter(([_, apis]) => apis.length > 1)
		.flatMap(([_, apis]) => apis);

	if (duplicates.length > 0) {
		console.warn('🚨 发现了相同名称的API请及时处理 ==>', { duplicates });
	}

	return duplicates.length === 0;
};

/**
 * 构建API对象
 * @returns {Object} API对象
 */
export const buildApiObject = () => {
	// 检测重复API
	const isValid = detectDuplicateApis();

	if (!isValid && process.env.NODE_ENV === 'development') {
		console.error('API注册存在重名问题，请检查并修复');
	}

	// 构建API对象
	return apiList.reduce((apis, api) => {
		const { urlName, fetchType = 'post', url, resType = 'json', timeout = '' } = api;
		// 返回请求方法
		apis[urlName] = (data, config = {}) => {
			const finalConfig = { resType, timeout, ...config };
			return requestMethods[fetchType](url, data, finalConfig);
		};

		return apis;
	}, {});
};

// 使用命名导出而不是默认导出，更符合ES6模块化规范
export { apiList as default };
