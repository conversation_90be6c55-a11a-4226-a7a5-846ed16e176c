<template>
	<div class="performanceDriver">
		<!-- 明细组件 -->
		<DetailCom ref="DetailComRef" :userList="userList" @close="queryTableData(1)" />
		<!-- 渠道/分销/代理下拉筛选 -->
		<ChannelSelect
			ref="ChannelSelect"
			type="inquiry"
			@change="
				searchForm.channelName = $event.channelName;
				searchForm.twidList = $event.twidList;
				queryTableData(1);
			"
		/>
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="询盘业绩与分配规则" name="performanceDriver">
				<BaseLayout>
					<template #header>
						<!-- 模糊查询 -->
						<SearchHistoryInput name="userName" placeholder="姓名" v-model.trim="searchForm.auname" @input="queryTableData(1)" />

						<SearchHistoryInput
							name="businessArea"
							placeholder="业务区域"
							v-model.trim="searchForm.businessArea"
							@input="queryTableData(1)"
						/>

						<el-radio-group v-model="searchForm.businessType" @change="queryTableData(1)">
							<el-radio :label="''">全部</el-radio>
							<el-radio :label="0">直销</el-radio>
							<el-radio :label="1">合伙</el-radio>
						</el-radio-group>
						<div class="ml-auto">
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
							<el-button type="text" class="icon-third-bt_newdoc" @click="openDetail('添加')">添加</el-button>
						</div>
					</template>
					<template #main>
						<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
						<div class="table-toolbar"></div>
						<!-- 表格主体 -->
						<u-table
							ref="uTableRef"
							class="table-main table-main2"
							:height="1200"
							:row-height="35"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							:highlight-current-row="false"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							@row-click="rowClick"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="60" type="index" align="center" fixed="left"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								:fixed="item.fixed"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- ========== 非编辑状态 ========== -->
									<div v-if="!scope.row.isEdit">
										<!-- 培训期1 -->
										<Tooltips
											v-if="item.colNo == 'trainingPeriod'"
											:cont-str="getTrainingPeriod(scope.row[item.colNo])"
											:cont-width="(scope.column.width || scope.column.realWidth) - 20"
										/>
										<!-- 培训期2 -->
										<Tooltips
											v-else-if="item.colNo == 'trainingPeriod2'"
											:cont-str="getTrainingPeriod(scope.row[item.colNo])"
											:cont-width="(scope.column.width || scope.column.realWidth) - 20"
										/>
										<!-- 培训期2 -->
										<Tooltips
											v-else-if="item.colNo == 'trainingPeriod3'"
											:cont-str="getTrainingPeriod(scope.row[item.colNo])"
											:cont-width="(scope.column.width || scope.column.realWidth) - 20"
										/>
										<!-- 业务类型 -->
										<Tooltips
											v-else-if="item.colNo == 'businessType'"
											:cont-str="businessTypeMap[scope.row[item.colNo]] || ''"
											:cont-width="(scope.column.width || scope.column.realWidth) - 20"
										/>
										<!-- 咨询类别 -->
										<Tooltips
											v-else-if="item.colNo == 'consultantType'"
											:cont-str="consultantTypeMap[scope.row[item.colNo]] || ''"
											:cont-width="(scope.column.width || scope.column.realWidth) - 20"
										/>
										<!-- 咨询级别 -->
										<Tooltips
											v-else-if="item.colNo == 'consultingLevel'"
											:cont-str="consultingLevelMap[scope.row[item.colNo]] || ''"
											:cont-width="(scope.column.width || scope.column.realWidth) - 20"
										/>
										<!-- 用户 -->
										<Tooltips
											v-else-if="userKey.includes(item.colNo)"
											:cont-str="getUserName(scope.row[item.colNo])"
											:cont-width="(scope.column.width || scope.column.realWidth) - 20"
										/>
										<!-- 添加百分比号 -->
										<Tooltips
											v-else-if="percentSignKey.includes(item.colNo) && scope.row[item.colNo]"
											:cont-str="scope.row[item.colNo] + '%'"
											:cont-width="(scope.column.width || scope.column.realWidth) - 20"
										/>
										<!-- 点评率规则 -->
										<Tooltips
											v-else-if="item.colNo == 'intervalReviewRateJson'"
											class="hover-green"
											:cont-str="getReviewRate(scope.row[item.colNo], 'str')"
											:cont-obj="getReviewRate(scope.row[item.colNo], 'obj')"
											:cont-width="(scope.column.width || scope.column.realWidth) - 20"
											@click.native="openDetail('编辑', scope.row)"
										/>
										<!-- 默认 -->
										<Tooltips
											v-else
											:cont-str="scope.row[item.colNo]"
											:cont-width="(scope.column.width || scope.column.realWidth) - 20"
										/>
									</div>
									<!-- ========== 编辑状态 ========== -->
									<Tooltips
										v-else-if="item.colNo == 'auname'"
										class="hover-green"
										@click.native="openDetail('编辑', scope.row)"
										:cont-str="scope.row[item.colNo]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 业务区域 -->
									<div v-else-if="item.colNo == 'businessArea'" class="flex-align-center">
										<el-input
											class="flex-1 input-border-none"
											v-model="scope.row[item.colNo]"
											:placeholder="item.colName"
											size="mini"
											@change="updateTableData(scope.row)"
										></el-input>

										<el-popover
											placement="right-start"
											width="300"
											trigger="click"
											@show="checkArea(scope.row)"
											@hide="checkAreaList = []"
										>
											<div class="label-title">业务区域内容校验</div>
											<div v-for="(area, index) in checkAreaList" :key="index">
												<span :class="area.warn ? 'red' : ''"> {{ index + 1 }}、{{ getAreaString(area) }} </span>
											</div>

											<i slot="reference" class="el-icon-place fs-14 mr10 pointer"></i>
										</el-popover>
									</div>

									<!-- 培训期1 -->
									<div v-else-if="item.colNo == 'trainingPeriod'" class="input-border-none">
										<el-date-picker
											v-model="scope.row[item.colNo]"
											type="daterange"
											range-separator="至"
											start-placeholder="开始日期"
											end-placeholder="结束日期"
											align="right"
											size="mini"
											value-format="timestamp"
											unlink-panels
											:picker-options="pickerOptions"
											@change="updateTableData(scope.row)"
										>
										</el-date-picker>
									</div>
									<!-- 培训期2 -->
									<div v-else-if="item.colNo == 'trainingPeriod2'" class="input-border-none">
										<el-date-picker
											v-model="scope.row[item.colNo]"
											type="daterange"
											range-separator="至"
											start-placeholder="开始日期"
											end-placeholder="结束日期"
											align="right"
											size="mini"
											value-format="timestamp"
											unlink-panels
											:picker-options="pickerOptions"
											@change="updateTableData(scope.row)"
										>
										</el-date-picker>
									</div>
									<!-- 培训期3 -->
									<div v-else-if="item.colNo == 'trainingPeriod3'" class="input-border-none">
										<el-date-picker
											v-model="scope.row[item.colNo]"
											type="daterange"
											range-separator="至"
											start-placeholder="开始日期"
											end-placeholder="结束日期"
											align="right"
											size="mini"
											value-format="timestamp"
											unlink-panels
											:picker-options="pickerOptions"
											@change="updateTableData(scope.row)"
										>
										</el-date-picker>
									</div>

									<!-- 业务类型 -->
									<el-radio-group
										v-else-if="item.colNo == 'businessType'"
										v-model="scope.row[item.colNo]"
										class="mini-radio"
										@change="updateTableData(scope.row)"
									>
										<el-radio v-for="i in Object.keys(businessTypeMap)" :key="i" :label="Number(i)">
											{{ businessTypeMap[i] }}
										</el-radio>
									</el-radio-group>
									<!-- 咨询类别 -->
									<el-select
										v-else-if="item.colNo == 'consultantType'"
										v-model="scope.row[item.colNo]"
										placeholder="咨询类别"
										size="mini"
										class="input-border-none"
										filterable
										@change="updateTableData(scope.row)"
									>
										<el-option
											v-for="i in Object.keys(consultantTypeMap)"
											:key="i"
											:label="consultantTypeMap[i]"
											:value="Number(i)"
										>
										</el-option>
									</el-select>
									<!-- 咨询级别 -->
									<el-select
										v-else-if="item.colNo == 'consultingLevel'"
										v-model="scope.row[item.colNo]"
										placeholder="咨询级别"
										size="mini"
										class="input-border-none"
										filterable
										@change="updateTableData(scope.row)"
									>
										<el-option
											v-for="i in Object.keys(consultingLevelMap)"
											:key="i"
											:label="consultingLevelMap[i]"
											:value="Number(i)"
										>
										</el-option>
									</el-select>

									<!-- 用户选择器 -->
									<div v-else-if="userKey.includes(item.colNo)">
										<el-select
											v-model="scope.row[item.colNo]"
											:placeholder="item.colName"
											size="mini"
											class="input-border-none"
											clearable
											filterable
											@change="updateTableData(scope.row)"
										>
											<el-option v-for="user in userList" :key="user.auid" :label="user.userName" :value="user.auid"> </el-option>
										</el-select>
									</div>

									<!-- 添加百分比号 -->
									<el-input
										v-else-if="percentSignKey.includes(item.colNo) && scope.row[item.colNo]"
										v-model="scope.row[item.colNo]"
										placeholder=""
										class="input-border-none input-right"
										size="mini"
										@change="updateTableData(scope.row)"
									>
										<i slot="suffix" class="">%</i>
									</el-input>

									<!-- 点评率规则 -->
									<Tooltips
										v-else-if="item.colNo == 'intervalReviewRateJson'"
										class="hover-green"
										:cont-str="getReviewRate(scope.row[item.colNo], 'str')"
										:cont-obj="getReviewRate(scope.row[item.colNo], 'obj')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
										@click.native="openDetail('编辑', scope.row)"
									/>
									<el-input
										v-else
										v-model="scope.row[item.colNo]"
										size="mini"
										class="input-border-none input-right"
										:placeholder="item.colName"
										@change="updateTableData(scope.row)"
									></el-input>
								</template>
							</u-table-column>
							<!-- 其他列/操作 -->
							<u-table-column label="" width="60" align="right" fixed="right">
								<template slot-scope="scope">
									<!-- <el-button type="text" class="el-icon-edit-outline" @click="openDetail('编辑', scope.row)"></el-button> -->
									<el-button type="text" class="el-icon-copy-document" @click="openDetail('复制', scope.row)"></el-button>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
			<el-tab-pane label="咨询评价分配规则" name="consultingAllocation">
				<ConsultingAllocation v-if="activeTab == 'consultingAllocation'" :userList="userList" />
			</el-tab-pane>
			<el-tab-pane label="全局规则" name="globalRule">
				<GlobalRule v-if="activeTab == 'globalRule'" />
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import { deepClone, debounce, dateFormat, jointString, sortTableData } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
// import DateSelect from '@/components/DateSelect/DateSelect'; //带建议日期选择组件
import ConsultingAllocation from './consultingAllocation.vue';
import GlobalRule from './globalRule.vue';
import DetailCom from './performanceDriverDetail.vue'; //明细组件
import ChannelSelect from '@/components/ChannelSelect.vue';

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		// DateSelect,
		ChannelSelect,
		ConsultingAllocation,
		GlobalRule,
		DetailCom,
	},
	name: 'performanceDriver', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			activeTab: 'performanceDriver', //激活tab页
			checkAreaList: [], //检验数据

			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tableDataCopy: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},

			// 查询表单
			searchForm: {
				auname: '',
				businessType: '',
				channelName: [],
				twidList: [],
				// 其他...
			},
			// 日期选择器
			pickerOptions: {
				shortcuts: [
					{
						text: '未来一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						},
					},
					{
						text: '未来一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						},
					},
					{
						text: '未来三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						},
					},
				],
			},
			// 用户选择器
			userKey: ['consultantMasterUid', 'businessPartnerUnitUid', 'businessPartnerUnitTechnicianUid'],
			// 添加百分比号
			percentSignKey: [
				'businessPartnerUnitMaster1stCommission',
				'businessPartnerUnitMaster2ndCommission',
				'businessPartnerUnitCommission',
				'businessPartnerUnitTechnicianCommission',
				'implementCommission',
				'consultingCommissionOwnself',
				'consultingCommissionDirectly',
				'consultingCommissionPartner',
				'salesCommission',
				'partnerTaxRate',
				'serviceCostRate',
			],

			// 业务类型
			businessTypeMap: {
				0: '直销',
				1: '合伙',
			},
			// 咨询类别
			consultantTypeMap: {
				0: '无',
				1: '自己',
				2: '直销',
				3: '合伙人',
				4: '不限',
			},
			// 咨询级别
			consultingLevelMap: {
				1: '初级咨询',
				2: '中级咨询',
				3: '高级咨询',
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos', 'userList']), //当前登录用户信息（含团队/菜单/权限等）

		// 根据业务类型过滤字段
		businessTypesFilter() {
			if (this.searchForm.businessType === 0) {
				return [
					'实战培训期1',
					'实战培训期2',
					'实战培训期3',
					'合伙税率%',
					'服务成本%',
					'MES供货价',
					'OEE供货价(元)',
					'MES续费(元)',
					'OEE续费(元)',
				]; //直销不需要显示的列
			} else if (this.searchForm.businessType === 1) {
				return ['月度目标(元)', '月达标奖励(元)', '业务提成%']; //合伙不需要显示的列
			}
			return [];
		},
		// 列过滤
		tableColumn() {
			const tableColumn = [
				{ colName: '姓名', colNo: 'auname', align: 'left', width: '120', fixed: 'left' },
				{ colName: '业务区域', colNo: 'businessArea', align: 'left', width: '300' },
				{ colName: '实战培训期1', colNo: 'trainingPeriod', align: 'center', width: '220' },
				{ colName: '实战培训期2', colNo: 'trainingPeriod2', align: 'center', width: '220' },
				{ colName: '实战培训期3', colNo: 'trainingPeriod3', align: 'center', width: '220' },
				{ colName: '业务类型', colNo: 'businessType', align: 'center', width: '130' },
				{ colName: '询盘池', colNo: 'inquiryPoolSize', align: 'right', width: '' },
				{ colName: '咨询类别', colNo: 'consultantType', align: 'left', width: '100' },
				{ colName: '咨询池', colNo: 'consultingPoolSize', align: 'right', width: '' },
				{ colName: '咨询级别', colNo: 'consultingLevel', align: 'left', width: '120' },
				{ colName: '评价数', colNo: 'inquiryEvaluateCount', align: 'right', width: '' },
				{ colName: '点评率', colNo: 'intervalReviewRateJson', align: 'center', width: '' },
				{ colName: '师带徒', colNo: 'consultantMasterUid', align: 'left', width: '150' },
				{ colName: '师带徒实战1期提成%', colNo: 'businessPartnerUnitMaster1stCommission', align: 'right', width: '150' },
				{ colName: '师带徒实战2期提成%', colNo: 'businessPartnerUnitMaster2ndCommission', align: 'right', width: '150' },
				{ colName: '师带徒实战3期提成%', colNo: 'businessPartnerUnitMaster3ndCommission', align: 'right', width: '150' },
				{ colName: '师带徒实战1期补贴', colNo: 'businessPartnerUnitMaster1ndSubsidy', align: 'right', width: '150' },
				{ colName: '师带徒实战2期补贴', colNo: 'businessPartnerUnitMaster2ndSubsidy', align: 'right', width: '150' },
				{ colName: '师带徒实战3期补贴', colNo: 'businessPartnerUnitMaster3ndSubsidy', align: 'right', width: '150' },
				{ colName: 'BPUL', colNo: 'businessPartnerUnitUid', align: 'left', width: '150' },
				{ colName: 'BPUL%', colNo: 'businessPartnerUnitCommission', align: 'right', width: '100' },
				{ colName: 'BPUT', colNo: 'businessPartnerUnitTechnicianUid', align: 'left', width: '150' },
				{ colName: 'BPUT补贴', colNo: 'businessPartnerUnitTechnicianSubsidy', align: 'right', width: '100' },
				{ colName: 'BPUT%', colNo: 'businessPartnerUnitTechnicianCommission', align: 'right', width: '100' },
				{ colName: '实施提成%', colNo: 'implementCommission', align: 'right', width: '100' },
				{ colName: '自有咨询提成%', colNo: 'consultingCommissionOwnself', align: 'right', width: '100' },
				{ colName: '直销咨询提成%', colNo: 'consultingCommissionDirectly', align: 'right', width: '100' },
				{ colName: '合伙咨询提成%', colNo: 'consultingCommissionPartner', align: 'right', width: '100' },
				{ colName: '月度目标(元)', colNo: 'monthlyGoals', align: 'right', width: '100' },
				{ colName: '月达标奖励(元)', colNo: 'monthlyAchievementRewards', align: 'right', width: '100' },
				{ colName: '业务提成%', colNo: 'salesCommission', align: 'right', width: '100' },
				{ colName: '合伙税率%', colNo: 'partnerTaxRate', align: 'right', width: '100' },
				{ colName: '服务成本%', colNo: 'serviceCostRate', align: 'right', width: '100' },
				{ colName: 'MES供货价(元)', colNo: 'supplierPriceMes', align: 'right', width: '' },
				{ colName: 'OEE供货价(元)', colNo: 'supplierPriceOee', align: 'right', width: '' },
				{ colName: 'MES续费(元)', colNo: 'renewalFeeMes', align: 'right', width: '' },
				{ colName: 'OEE续费(元)', colNo: 'renewalFeeOee', align: 'right', width: '' },
			];
			if (this.businessTypesFilter.length > 0) {
				return tableColumn.filter(item => !this.businessTypesFilter.includes(item.colName));
			}
			return tableColumn;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 行点击时增加编辑状态
		rowClick(row) {
			if (!row.isEdit) {
				row.isEdit = true;
				this.sortChange(this.tableSort, true);
			}
		},
		// 获取点评率
		getReviewRate(reviewRate, type = 'str') {
			if (type === 'str') {
				return reviewRate.length ? reviewRate[reviewRate.length - 1].requiredReviewRate : '';
			}
			return reviewRate.map(item => {
				return {
					title: `${dateFormat(item.startTime, 'line')} ~ ${dateFormat(item.endTime, 'line')}`,
					content: item.requiredReviewRate + '%',
				};
			});
		},
		// 获取用户名
		getUserName(uid) {
			const user = this.userList.find(item => item.auid === uid);
			return user ? user.userName : '';
		},
		// 获取培训期
		getTrainingPeriod(trainingPeriod) {
			if (!trainingPeriod || trainingPeriod.length !== 2) {
				return '';
			}
			return `${dateFormat(trainingPeriod[0], 'line')} 至 ${dateFormat(trainingPeriod[1], 'line')}`;
		},
		// 获取区域名称
		getAreaString(areaObj) {
			if (!areaObj) {
				return '';
			}
			if (!areaObj.parent) {
				return areaObj.name;
			}

			const parentString = this.getAreaString(areaObj.parent);
			const areaString = `${areaObj.name}`;

			return parentString ? `${parentString} - ${areaString}` : areaString;
		},
		// 校验业务区域
		checkArea: debounce(async function (row) {
			const API = 'fetchArea';
			const DATA = row.businessArea?.split(',');
			try {
				const res = await this.$axios[API](JSON.stringify(DATA));
				// 是否存在空值补充空对象
				this.checkAreaList =
					res.data?.map(item => {
						if (!item) {
							item = { name: '* 未找到该区域，请检查！', warn: true };
						}
						return item;
					}) || [];
			} catch (error) {
				this.checkAreaList = [];

				console.error(`${API} |` + error);
			}
		}),
		// 保存修改信息
		updateTableData(row) {
			if (!row.businessArea) {
				return this.$message.warning('业务区域不允许为空！');
			}
			// 实战培训期1
			if (row.trainingPeriod && row.trainingPeriod?.length > 0) {
				row.businessProbationStart = row?.trainingPeriod[0];
				row.businessProbationEnd = row?.trainingPeriod[1];
			} else {
				row.businessProbationStart = '';
				row.businessProbationEnd = '';
			}

			// 实战培训期2
			if (row.trainingPeriod2 && row.trainingPeriod2?.length > 0) {
				row.businessProbation2ndStart = row?.trainingPeriod2[0];
				row.businessProbation2ndEnd = row?.trainingPeriod2[1];
			} else {
				row.businessProbation2ndStart = '';
				row.businessProbation2ndEnd = '';
			}

			// 实战培训期3
			if (row.trainingPeriod3 && row.trainingPeriod3?.length > 0) {
				row.businessProbation3ndStart = row?.trainingPeriod3[0];
				row.businessProbation3ndEnd = row?.trainingPeriod3[1];
			} else {
				row.businessProbation3ndStart = '';
				row.businessProbation3ndEnd = '';
			}

			// 将业务区域的，都转换成，半角字符,
			row.businessArea = row.businessArea?.replace(/，/g, ',');
			// 点评率规则（JSON字符串） 
			const intervalReviewRateJson =  JSON.stringify(row.intervalReviewRateJson);
			const API = 'updateInquiryAllocationPerformanceIndicator';
			this.$axios[API](JSON.stringify({ ...row, intervalReviewRateJson }))
				.then(res => {
					if (res.data.success) {
						this.$succ('保存成功！');
						// this.queryTableData(1);
					} else {
						this.$err(res.data.message);
						this.queryTableData(1);
					}
				})
				.catch(error => {
					console.log(`${API} | ` + error);
				});
		},

		// 打开明细
		openDetail(action, row) {
			this.$refs.DetailComRef.showDetailCom(action, row, this.searchForm.businessType);
		},
		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'selectInquiryAllocationPerformanceIndicatorList'; //接口
			this.$axios[API](JSON.stringify(this.searchForm))
				.then(res => {
					if (res.data.success) {
						const getTrainingPeriod = (start, end) => (!start || !end ? [] : [start, end]);
						this.tableData = res.data.data.map(item => {
							// 实战培训期
							item.trainingPeriod = getTrainingPeriod(item.businessProbationStart, item.businessProbationEnd);
							// 实战培训期2
							item.trainingPeriod2 = getTrainingPeriod(item.businessProbation2ndStart, item.businessProbation2ndEnd);
							// 实战培训期3
							item.trainingPeriod3 = getTrainingPeriod(item.businessProbation3ndStart, item.businessProbation3ndEnd);

							// 点评率规则（JSON字符串）
							item.intervalReviewRateJson = item.intervalReviewRateJson
								? JSON.parse(item.intervalReviewRateJson)?.map(item => {
										return {
											...item,
											reviewRateTime: item.reviewRateTime ? [item.startTime, item.endTime] : [],
										};
									}) || []
								: [];

							return item;
						});
						this.tableDataCopy = this.tableData;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),

		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				this.tableData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableDataCopy;
				this.$refs?.uTableRef?.reloadData(this.tableData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},

		dateFormat, //日期format
	},
};
</script>

<style lang="scss">
.performanceDriver {
	width: 100%;
	overflow: hidden;
	position: relative;
	.table-wrapper .table-main2 .el-table__body td {
		// height: 49px !important;
		height: 35px !important;
		padding: 0px;
		.el-input__inner {
			padding-left: 0px;
		}

		.el-range-input {
			width: 65px !important;
			background: transparent !important;
		}
	}
	.table-wrapper .table-main2 .is-right {
		.tdNormal {
			padding-right: 10px;
		}
	}
	.el-table--striped .el-table__body tr.el-table__row--striped td {
		background: #f2f2f2;
		.el-input__inner {
			background: #f2f2f2 !important;
		}
	}
	// .el-table__body tr.hover-row > td {
	// 	background-color: #fff !important;
	// }
}
</style>
