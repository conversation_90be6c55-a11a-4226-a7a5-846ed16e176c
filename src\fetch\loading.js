/**
 * Loading 状态管理模块
 */
import LoadingService from '@/components/Loading/loading-service';

// 默认配置
const DEFAULT_OPTIONS = {
	text: 'LightMES',
	showMask: true,
	wordList: ['树字标品MES', '让智造简单些', '手机管工厂', '就找郑校长'],
};

// 默认延迟显示时间loading（毫秒）
export const DEFAULT_DELAY = 1500;

class LoadingManager {
	constructor() {
		this.service = null;
		this.count = 0;
		this.timer = null;
		this.startTime = 0;
		this.minDisplayTime = 500; // 最小显示时间，避免闪烁
	}

	/**
	 * 开始加载
	 * @param {Object} options - 自定义配置项
	 * @param {number} delay - 延迟显示时间
	 */
	start(options = {}, delay = DEFAULT_DELAY) {
		this.count++;

		// 已经显示 loading，直接返回
		if (this.service && this.service.instance) {
			return;
		}

		// 清除之前的定时器
		if (this.timer) {
			clearTimeout(this.timer);
		}

		// 合并选项
		const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
		// 设置延迟显示定时器
		this.timer = setTimeout(() => {
			this.startTime = Date.now();
			this.service = new LoadingService(mergedOptions);
			this.service.show();
		}, delay);
	}

	/**
	 * 结束加载
	 */
	close() {
		if (this.count > 0) {
			this.count--;
		}

		if (this.count === 0) {
			// 清除延迟显示定时器
			if (this.timer) {
				clearTimeout(this.timer);
				this.timer = null;
			}

			// 如果 loading 已经显示，需要考虑最小显示时间
			if (this.service && this.service.instance) {
				const remainTime = this.minDisplayTime - (Date.now() - this.startTime);
				if (remainTime > 0) {
					setTimeout(() => {
						this.closeService();
					}, remainTime);
				} else {
					this.closeService();
				}
			}
		}
	}

	/**
	 * 强制关闭所有 loading
	 */
	closeAll() {
		if (this.timer) {
			clearTimeout(this.timer);
			this.timer = null;
		}

		if (this.service && this.service.instance) {
			this.closeService();
		}

		this.count = 0;
	}

	/**
	 * 关闭 loading 实例
	 */
	closeService() {
		if (this.service) {
			this.service.close();
			this.service = null;
		}
		this.startTime = 0;
	}
}

export const LoadingInstance = new LoadingManager();

export default { LoadingInstance, DEFAULT_DELAY };
