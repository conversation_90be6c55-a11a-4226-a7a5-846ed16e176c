{"name": "ops-admin", "version": "1.0.0", "description": "OPS管理系统", "author": "hansen <<EMAIL>>", "private": true, "scripts": {"dev": "npx webpack serve --config build/webpack.dev.conf.js", "build": "node build/build.js && node scripts/zip.js", "build:deploy": "node build/build.js && node scripts/zip.js && node scripts/deploy.js"}, "dependencies": {"@jiaminghi/data-view": "^2.10.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/babel-preset-jsx": "^1.4.0", "@wangeditor/editor": "^5.1.18", "@wangeditor/editor-for-vue": "^1.0.2", "@wangeditor/plugin-upload-attachment": "^1.1.0", "axios": "^0.21.1", "big.js": "^6.2.2", "caniuse-lite": "^1.0.30001450", "chalk": "^4.1.2", "echarts": "^5.5.1", "element-ui": "^2.15.14", "inquirer": "^6.5.2", "js-base64": "^3.7.5", "js-cookie": "^3.0.5", "jsbarcode": "^3.11.6", "jsencrypt": "^3.3.2", "lodash": "^4.17.21", "moment": "^2.29.1", "print-js": "^1.6.0", "qrcode": "^1.5.4", "sortablejs": "^1.13.0", "umy-ui": "^1.1.6", "vue": "^2.7.16", "vue-count-to": "^1.0.13", "vue-router": "^3.5.1", "vue-splitpane": "^1.0.6", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "xlsx": "^0.17.0"}, "devDependencies": {"@babel/core": "^7.21.4", "@babel/plugin-transform-modules-commonjs": "^7.21.2", "@babel/plugin-transform-runtime": "^7.21.4", "@babel/preset-env": "^7.21.4", "@babel/runtime-corejs3": "^7.28.2", "@plugin-web-update-notification/webpack": "^1.7.1", "@swc/core": "^1.3.44", "archiver": "^7.0.1", "autoprefixer": "^10.4.14", "babel-loader": "^9.1.2", "code-inspector-plugin": "^1.0.2", "compression-webpack-plugin": "^10.0.0", "copy-webpack-plugin": "^11.0.0", "core-js": "^3.29.1", "css-loader": "^6.7.3", "css-minimizer-webpack-plugin": "^5.0.0", "element-china-area-data": "^5.0.2", "eslint": "^8.57.1", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^9.17.0", "html-webpack-plugin": "^5.5.0", "image-webpack-loader": "^8.1.0", "mini-css-extract-plugin": "^2.7.5", "moment-locales-webpack-plugin": "^1.2.0", "postcss-import": "^15.1.0", "postcss-loader": "^7.1.0", "postcss-preset-env": "^9.1.3", "postcss-url": "^10.1.3", "prettier": "^3.0.2", "process": "^0.11.10", "sass": "^1.89.2", "sass-loader": "^13.2.2", "speed-measure-webpack-plugin": "^1.5.0", "ssh2-sftp-client": "^11.0.0", "style-loader": "^3.3.2", "terser-webpack-plugin": "^5.3.7", "vue-eslint-parser": "^9.3.1", "vue-loader": "^15.10.1", "vue-template-compiler": "^2.7.14", "webpack": "^5.77.0", "webpack-bundle-analyzer": "^4.8.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.13.2", "webpack-merge": "^5.8.0", "webpackbar": "^5.0.2"}, "engines": {"node": ">= 18.0.0", "pnpm": ">= 8.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}