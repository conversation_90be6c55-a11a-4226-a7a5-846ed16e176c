<template>
	<div class="SettlementPrinter" :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<div class="detail-head">
				<span class="detail-title">{{ titleName }}</span>
				<div>
					<el-button type="text" class="el-icon-printer" @click="printOrder">打印</el-button>
					<el-button type="text" class="el-icon-arrow-left" @click="closeDetailCom">返回</el-button>
				</div>
			</div>
			<!-- 明细详情弹窗 -->
			<div id="PrintWrapper" class="detail-content pl20 pr20">
				<div id="PrintBody">
					<div class="print-table">
						<table cellpadding="5" cellspacing="0">
							<!-- 表头 -->
							<thead>
								<tr>
									<td :colspan="tableColumn.length + 1">
										<div class="table-title center">
											<img src="@/assets/img/lightmes-logo.webp" alt="logo" class="logo" />
											<h1>树字MES合伙人结算单（{{ dateFormat(new Date(searchForm.dateStart), 'YM') }}）</h1>
										</div>
									</td>
								</tr>
								<tr>
									<th class="center">序号</th>
									<th v-for="(cItem, cIndex) in tableColumn" :key="cIndex" :class="cItem.class">{{ cItem.colName }}</th>
								</tr>
							</thead>
							<!-- 表体 -->
							<tbody>
								<tr
									v-for="(row, rowIndex) in tableData.length < 35
										? tableData.concat(new Array(35 - tableData.length).fill({}))
										: tableData"
									:key="rowIndex"
									class="table-row"
									:class="{ 'zebra-row': rowIndex % 2 !== 0 && row['合伙人'] }"
								>
									<td class="center">{{ rowIndex < tableData.length ? rowIndex + 1 : '' }}</td>
									<td v-for="(cItem, cIndex) in tableColumn" :key="cIndex" :class="cItem.class">
										<span v-if="cItem.colNo == '合伙人'" class="max-w-100 ellipsis">
											{{ row && row[cItem.colNo] ? row[cItem.colNo] : '' }}
										</span>
										<span v-else-if="cItem.colNo == '_total'">
											{{ row && row[cItem.colNo] ? row[cItem.colNo].toFixed(2) : '' }}
										</span>
										<span v-else>
											{{ row && row[cItem.colNo] ? row[cItem.colNo] : '' }}
										</span>
									</td>
								</tr>
								<tr>
									<td :colspan="tableColumn.length + 1" class="right"> 合计：{{ getTotalAmount(tableData) }} </td>
								</tr>
							</tbody>
							<!-- 表尾 -->
							<tfoot>
								<tr>
									<td colspan="2" class="left">制表日期：{{ dateFormat(new Date(), 'line') }} </td>
									<td colspan="2" class="left">制表人：{{ userInfos?.adminUserVO.userName }} </td>
									<td colspan="2" class="left">业务总监审核:</td>
									<td colspan="2" class="left">运营总监审核：</td>
									<td colspan="4" class="left">总经理审核：</td>
									<td colspan="5" class="left">董事长审核：</td>
								</tr>
							</tfoot>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { debounce, dateFormat, jointString } from '@/util/tool';
import { bigAdd } from '@/util/math';
import { mapGetters } from 'vuex';
import printJS from 'print-js';
export default {
	name: 'SettlementPrinter',
	props: {
		searchForm: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {
			showCom: false, //控制弹窗显隐
			titleName: '',
			tableData: [],

			tableColumn: [
				{ colName: '合伙人', colNo: '合伙人', class: 'left W8' },
				{ colName: '分成', colNo: '分成', class: 'right W8' },
				{ colName: 'BPUL', colNo: 'BPUL', class: 'right' },
				{ colName: '支援本部交付', colNo: '差旅：支援本部交付', class: 'right ' },
				{ colName: '支援本部商务', colNo: '差旅：支援本部商务', class: 'right ' },
				{ colName: '本部支援交付', colNo: '差旅：本部支援交付', class: 'right ' },
				{ colName: '本部支援商务', colNo: '差旅：本部支援商务', class: 'right ' },
				{ colName: '报销支援本部', colNo: '报销：支援本部', class: 'right W8' },
				{ colName: '报销本部支援', colNo: '报销：本部支援', class: 'right W8' },
				{ colName: '补贴', colNo: '补贴', class: 'right' },
				{ colName: '话费', colNo: '话费', class: 'right ' },
				{ colName: '运费', colNo: '运费', class: 'right' },
				{ colName: '其他', colNo: '其他', class: 'right', class: 'right W8' },
				{ colName: '合计', colNo: '_total', class: 'right W10' },
			],
		};
	},
	created() {},
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	watch: {
		showCom(val) {
			if (!val) {
				this.$emit('close');
			}
		},
	},
	mounted() {},
	methods: {
		// 计算合计金额
		getTotalAmount(tableData) {
			return tableData.reduce((total, item) => {
				return bigAdd(total, Number(item._total), 2);
			}, 0);
		},

		printOrder() {
			const style = `
				@page {
						size: A4;
						margin:0 5mm;
				}
        @media print{
					@page {
							size: landscape;
					}
				}
				#PrintBody {
					font-size: 8pt;
					box-sizing: border-box;
					width: 100%;
				}
				#PrintBody .table-title {
					position: relative;
					display: flex;
					align-items: center;
					justify-content: center;
					img {
						height: 40px;
						position: absolute;
						left: 0;
						top: 15px;
					}
					h1 {
						margin: 20px auto;
					}
				}
				#PrintBody .print-table table {
					width: 100%;
					table-layout: auto;
				}
			#PrintBody .print-table table th {
				text-align: left; /* 左对齐文本 */
				white-space: nowrap; /* 不换行 */
				overflow: hidden; /* 超出部分隐藏 */
				text-overflow: ellipsis; /* 超出部分用省略号表示 */
				border-collapse: collapse; /* 表格合并边框 */
				border-top: 1px solid gray; /* 上边框 */
				border-bottom: 1px solid gray; /* 下边框 */
				background-color: #f2f2f2; /* 设置标题背景色 */
			}
				#PrintBody .print-table table td {
				text-align: right;
				font-size: 8pt;
				word-break: break-all;
				padding: 0 5px !important;
			}
				.table-row.zebra-row {
					background-color: #f3f3f3; /* 设置斑马纹背景色 */
				}

					#PrintBody .print-table table tfoot td {
					height: 120px;
					border-top: 1px solid gray;
				}

					#PrintBody .print-table table .left {
						text-align: left !important;
					}
					#PrintBody .print-table table thead {
						display: table-header-group; /* 这个属性使thead总是在新的page-break之后重新开始 */
					}
					#PrintBody .print-table table .right {
						text-align: right !important;
					}
					#PrintBody .print-table table .center {
						text-align: center !important;
					}


					#PrintBody .W5 {
						width: 5% !important;
					}
					#PrintBody .W8 {
						width: 8% !important;
					}
					#PrintBody .W10 {
						width: 10% !important;
					}
					#PrintBody .W15 {
						width: 15% !important;
					}
					#PrintBody .W20 {
						width: 20% !important;
					}

					#PrintBody .print-table table th {
						width: 5%;
					}
					#PrintBody .print-table table th:first-child {
						width: 3% !important;
					}
					#PrintBody .max-w-100 {
						max-width: 100px;
					}
					#PrintBody .ellipsis {
						display: inline-block;
						white-space: nowrap;
						text-overflow: ellipsis;
						overflow: hidden;
					}

        `;
			printJS({
				printable: 'PrintWrapper',
				type: 'html',
				// css: PrinterStyle,
				documentTitle: '合伙人结算单',
				scanStyles: false,
				style: style,
				targetStyles: ['*'],
				font: 'Microsoft YaHei',
			});
		},

		//显示弹窗
		showDetailCom: debounce(async function (PRINT_DATA) {
			this.titleName = '合伙人结算单';
			this.tableData = PRINT_DATA.filter(i => i._total);
			this.showCom = true; //不打开弹窗直接打印
			console.log({ PRINT_DATA });
		}, 500),

		//点击返回
		closeDetailCom() {
			this.showCom = false;
			this.tableData = [];
		},
		//日期format
		dateFormat: dateFormat,
		jointString: jointString,
	},
};
</script>
<style lang="scss" scoped>
/* 在此处调完之后打印样式后再复制到printJS的style字符串中（注意是css样式） */
.SettlementPrinter {
	#PrintBody {
		font-size: 8pt;
		box-sizing: border-box;
		width: 100%;
	}

	#PrintBody .table-title {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		img {
			height: 40px;
			position: absolute;
			left: 0;
			top: 15px;
		}
		h1 {
			margin: 20px auto;
		}
	}

	#PrintBody .print-table table {
		width: 100%;
		table-layout: auto;
	}
	#PrintBody .print-table table th {
		text-align: left; /* 左对齐文本 */
		white-space: nowrap; /* 不换行 */
		overflow: hidden; /* 超出部分隐藏 */
		text-overflow: ellipsis; /* 超出部分用省略号表示 */
		border-collapse: collapse; /* 表格合并边框 */
		border-top: 1px solid gray; /* 上边框 */
		border-bottom: 1px solid gray; /* 下边框 */
		background-color: #f2f2f2; /* 设置标题背景色 */
	}
	#PrintBody .print-table table td {
		text-align: right;
		font-size: 8pt;
		min-height: 20px !important;
		word-break: break-all;
		padding: 0 5px !important;
	}
	.table-row.zebra-row {
		background-color: #f3f3f3; /* 设置斑马纹背景色 */
	}

	#PrintBody .print-table table tfoot td {
		height: 120px;
		border-top: 1px solid gray;
	}

	#PrintBody .print-table table .left {
		text-align: left !important;
	}
	#PrintBody .print-table table thead {
		display: table-header-group; /* 这个属性使thead总是在新的page-break之后重新开始 */
	}
	#PrintBody .print-table table .right {
		text-align: right !important;
	}
	#PrintBody .print-table table .center {
		text-align: center !important;
	}

	#PrintBody .W5 {
		width: 5% !important;
	}
	#PrintBody .W8 {
		width: 8% !important;
	}
	#PrintBody .W10 {
		width: 10% !important;
	}
	#PrintBody .W15 {
		width: 15% !important;
	}
	#PrintBody .W20 {
		width: 20% !important;
	}

	#PrintBody .print-table table th {
		width: 5%;
	}
	#PrintBody .print-table table th:first-child {
		width: 3% !important;
	}
	#PrintBody .max-w-100 {
		max-width: 100px;
	}
	#PrintBody .ellipsis {
		display: inline-block;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
	}
}
</style>
