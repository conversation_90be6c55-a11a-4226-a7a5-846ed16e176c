'use strict';
// 开发环境变量（导出为字符串（带引号），否则 webpack.DefinePlugin 注入会出错）
module.exports = {
	NODE_ENV: '"development"', // 开发环境名称
	PORT: 8082, // 开发环境端口
	API_HOST: '"http://ops.lightmes.cn/"', // 生产环境
	API_ADDRESS: '"ops.lightmes.cn/"',
	// API_HOST: '"http://test.m.lightmes.cn/"', // 旧测试环境
	// API_ADDRESS: '"test.m.lightmes.cn/"',
	// API_HOST: '"https://ops.test.base.lightmes.cn/"', // 测试环境
	// API_ADDRESS: '"ops.test.base.lightmes.cn/"',
	// API_HOST: '"http://dev.ops.lightmes.com/"', // 开发环境
	// API_ADDRESS: '"dev.ops.lightmes.com/"',
};
