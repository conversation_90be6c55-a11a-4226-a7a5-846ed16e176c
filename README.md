# 树字OPS运营管理系统

[![Vue.js](https://img.shields.io/badge/Vue.js-2.x-4FC08D?style=flat-square&logo=vue.js)](https://vuejs.org/)
[![Vue Router](https://img.shields.io/badge/Vue%20Router-3.5.1-4FC08D?style=flat-square)](https://router.vuejs.org/)
[![Vuex](https://img.shields.io/badge/Vuex-3.6.2-4FC08D?style=flat-square)](https://vuex.vuejs.org/)
[![Element UI](https://img.shields.io/badge/Element%20UI-2.14.1-409EFF?style=flat-square)](https://element.eleme.io/)
[![Node.js](https://img.shields.io/badge/Node.js-18.x-339933?style=flat-square&logo=node.js)](https://nodejs.org/)
[![Webpack](https://img.shields.io/badge/Webpack-5.x-8DD6F9?style=flat-square&logo=webpack)](https://webpack.js.org/)
[![pnpm](https://img.shields.io/badge/pnpm-8.x-F69220?style=flat-square&logo=pnpm)](https://pnpm.io/)
[![Axios](https://img.shields.io/badge/Axios-0.21.1-5A29E4?style=flat-square&logo=axios)](https://axios-http.com/)
[![ECharts](https://img.shields.io/badge/ECharts-5.5.1-AA344D?style=flat-square)](https://echarts.apache.org/)
[![Lodash](https://img.shields.io/badge/Lodash-4.17.21-3492DB?style=flat-square&logo=lodash)](https://lodash.com/)
[![Moment.js](https://img.shields.io/badge/Moment.js-2.29.1-5A29E4?style=flat-square)](https://momentjs.com/)
[![Babel](https://img.shields.io/badge/Babel-7.x-F9DC3E?style=flat-square&logo=babel)](https://babeljs.io/)
[![Sass](https://img.shields.io/badge/Sass-1.89.2-CC6699?style=flat-square&logo=sass)](https://sass-lang.com/)
[![PostCSS](https://img.shields.io/badge/PostCSS-10.4.14-DD3A0A?style=flat-square&logo=postcss)](https://postcss.org/)
[![ESLint](https://img.shields.io/badge/ESLint-8.47.0-4B32C3?style=flat-square&logo=eslint)](https://eslint.org/)
[![Prettier](https://img.shields.io/badge/Prettier-3.0.2-F7B93E?style=flat-square&logo=prettier)](https://prettier.io/)
[![QRCode](https://img.shields.io/badge/QRCode-1.5.4-000000?style=flat-square)](https://github.com/davidshimjs/qrcodejs)
[![License](https://img.shields.io/badge/License-Proprietary-red?style=flat-square)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen?style=flat-square)]()

> 🏭 树字OPS运营管理系统是一款专为企业量身打造的数字化运营管理平台。

## 📋 目录

- [项目介绍](#-项目介绍)
- [功能特性](#-功能特性)
- [快速开始](#-快速开始)
- [技术栈](#-技术栈)
- [项目结构](#-项目结构)
- [开发指南](#-开发指南)
- [部署说明](#-部署说明)
- [API文档](#-api文档)
- [浏览器支持](#-浏览器支持)
- [常见问题](#-常见问题)
- [许可证](#-许可证)

## 🎯 项目介绍

树字OPS运营管理系统是一个专为公司打造的运营管理平台。系统集成了流量运营、业务运营、合同管理、业绩管理、项目管理、开发管理、交付管理、设备管理、团队管理等核心功能，为企业提供全方位的数字化运营管理解决方案。

## ✨ 功能特性

### 🏢 核心业务模块

#### 📊 数据驾驶舱管理
- **多维度驾驶舱**: 业务驾驶舱、交付驾驶舱、开发驾驶舱、测试驾驶舱、管理者驾驶舱
- **实时数据监控**: KPI指标展示，运营状态可视化
- **步骤流程管理**: 开发步骤、交付步骤跟踪
- **积分日志管理**: 员工积分记录和统计

#### 👤  询盘和客户管理
- **渠道设置**: 询盘渠道配置和管理
- **商机备案**: 询盘登记备案、智能分配、咨询转化管理
- **询盘跟单**: 客户跟进记录和跟进计划
- **流量推广**: 流量部门报工配置和视频->询盘转化分析
- **延期审核**: 询盘延期申请审核
- **成交客户保护**: 已成交客户保护机制
- **客户列表**: 已备案的客户信息管理和查询
- **到期管理**: 客户备案日期到期提醒和管理
- **行业案例**: 行业案例管理和展示

#### 🚚 合同和交付管理
- **合同管理**: 合同创建、审批、结算、执行监控
- **交付管理**: 交付计划制定、进度跟踪、质量管控
- **订单管理**: 订单全生命周期管理，状态跟踪
- **回款管理**: 回款计划、回款记录、回款分析
- **延期申请**: 项目交付延期申请和审批流程
  
#### 🎯 绩效管理
- **个人积分**: 员工积分制度、积分明细、积分审核
- **健康度评估**: BP健康度评估和管理
- **绩效驱动**: 绩效规则配置、咨询分配
- **结算管理**: 绩效结算、结算明细、结算打印
- **结算列表**: 结算记录查询和管理
  
#### 🛫 差旅管理
- **差旅审批**: 差旅申请审批流程
- **费用报销**: 差旅费用报销管理
- **报销审核**: 报销单据审核流程
  
#### 💻 开发管理
- **需求管理**: 需求收集、分析、优先级排序
- **项目管理**: 项目规划、任务分配、进度监控
- **开发管理**: 开发任务分配、进度跟踪、工作量（工时）统计
- **测试管理**: 测试计划、缺陷管理、测试报告
- **年度系数管理**: 员工年度绩效系数设置
- **缺陷管理**: 缺陷跟踪、分析、修复验证
  
#### 👥 团队管理
- **团队数据维护**: 团队基础信息管理
- **团队审批**: 团队相关审批流程

#### 🏭 设备管理
- **固件版本**: 设备固件版本管理和更新
- **智能三色灯**: 三色灯设备管理、用户数据、注册等
- **安灯盒子管理**: 安灯盒子设备列表、固件版本管理
- **安灯手环管理**: 手环设备列表、盒子管理

#### 🎓 培训管理
- **培训计划**: 培训计划制定和管理
- **课程制作**: 培训课程内容制作
- **在线培训**: 在线培训平台管理
- **培训进度**: 培训进度跟踪和评估

#### 👨‍💼 人力资源
- **在线测试**: 在线考试系统管理
- **题库管理**: 考试题库维护和管理

#### 🗺️ 看板管理
- **业务地图**: 业务分布地图展示
- **综合看板**: 多维度数据综合展示
- **运营看板**: 运营数据可视化展示

#### ⚙️ 系统管理
- **用户管理**: 系统用户权限管理
- **在线支持**: 在线客服支持系统
- **问答管理**: 常见问题问答管理
- **资源描述**: 系统资源和字段描述管理

### 🛠️ 技术特性

- **前端框架**: Vue.js 2.x + Element UI
- **状态管理**: Vuex 集中状态管理
- **路由管理**: Vue Router 单页应用路由
- **HTTP通信**: Axios 请求库
- **构建工具**: Webpack 5 现代化构建
- **代码规范**: ESLint + Prettier 

## 🚀 快速开始

### 环境要求

- **Node.js**: >= 18.x.x
- **pnpm**: >= 8.x.x
- **Git**: 最新版本

### 为什么选择 pnpm？

- **🚀 更快的安装速度**: 比npm快2-3倍
- **💾 节省磁盘空间**: 使用硬链接和符号链接，节省大量空间
- **🔒 更严格的依赖管理**: 防止幽灵依赖，确保项目稳定性
- **📦 更好的monorepo支持**: 原生支持monorepo项目
- **🛡️ 更安全**: 内置安全检查和审计功能

### 安装步骤

```bash
# 1. 克隆项目
git clone https://gps.lightmes.cn/web/ops.git

# 2. 进入项目目录
cd ops

# 3. 安装依赖
pnpm install

# 4. 启动开发服务器
pnpm dev
```

### 访问应用

启动成功后，在浏览器中访问：
- **本地开发**: http://dev.ops.lightmes.com
- **测试环境**: https://ops.test.base.lightmes.cn
- **生产环境**: http://ops.lightmes.cn

## 🛠️ 技术栈

### 前端技术

| 技术 | 版本 | 说明 |
|------|------|------|
| Vue.js | 2.x | 渐进式JavaScript框架 |
| Element UI | 2.14.1 | 基于Vue的组件库 |
| Vue Router | 3.x | 官方路由管理器 |
| Vuex | 3.x | 状态管理模式 |
| Axios | 0.21.1 | HTTP客户端 |
| ECharts | 5.5.1 | 数据可视化图表库 |

### 开发工具

| 工具 | 版本 | 说明 |
|------|------|------|
| Node.js | 18.x | JavaScript运行环境 |
| pnpm | 8.x | 快速、节省磁盘空间的包管理器 |
| Webpack | 5.x | 项目构建工具 |
| ESLint | - | 代码质量检查 |
| Prettier | - | 代码格式化 |

### 构建工具

- **Webpack**: 模块打包工具
- **Babel**: JavaScript编译器
- **PostCSS**: CSS后处理器
- **Sass**: CSS预处理器

## 📁 项目结构

```
ops/
├── 📁 .vscode/                 # VSCode 配置
├── 📁 build/                   # Webpack 构建配置
├── 📁 config/                  # 环境配置文件
├── 📁 dist/                    # 构建输出目录
├── 📁 scripts/                 # 构建脚本
│   ├── 📄 deploy.js            # 部署脚本
│   └── 📄 zip.js               # 打包脚本
├── 📁 src/                     # 源代码目录
│   ├── 📁 assets/              # 静态资源
│   │   ├── 📁 fonts/           # 字体文件
│   │   │   ├── 📁 office-font/ # office办公软件icon
│   │   │   ├── 📁 ops-font/    # OPS-icon
│   │   │   └── 📁 third-font/  # 原方案-icon
│   │   ├── 📁 img/             # 图片资源
│   │   └── 📁 js/              # 工具脚本
│   │       ├── 📄 contractSource.js    # 合同数据源
│   │       ├── 📄 inquirySource.js     # 询盘数据源
│   │       ├── 📄 LodopFuncs.js        # 打印功能
│   │       └── 📄 projectSource.js     # 项目数据源
│   ├── 📁 components/          # 全局组件
│   │   ├── 📁 AudioPlayer/     # 音频播放器
│   │   ├── 📁 BaseTableForm/   # 基础表格表单
│   │   ├── 📁 Chart/           # 图表组件
│   │   ├── 📁 DateSelect/      # 日期选择器
│   │   ├── 📁 ExportTable/     # 表格导出
│   │   ├── 📁 ImportTable/     # 表格导入
│   │   ├── 📁 InquiryAudioPlayer/ # 询盘音频播放器
│   │   ├── 📁 InquiryDetail/   # 询盘详情
│   │   ├── 📁 InquiryDetail_JXC/ # 询盘详情（聚心城）
│   │   ├── 📁 Loading/         # 加载组件
│   │   ├── 📁 Teleport/        # 传送门组件
│   │   ├── 📁 WangEditor/      # 富文本编辑器
│   │   ├── 📄 BaseLayout.vue   # 基础布局
│   │   ├── 📄 CaseFiles.vue    # 案例文件
│   │   ├── 📄 ChannelSelect.vue # 渠道选择
│   │   ├── 📄 ExpandTable.vue  # 展开表格
│   │   ├── 📄 ExpandableList.vue # 可展开列表
│   │   ├── 📄 FilePopover.vue  # 文件弹出框
│   │   ├── 📄 FilePreview.vue  # 文件预览
│   │   ├── 📄 InquriyList.vue  # 询盘列表
│   │   ├── 📄 SearchHistoryInput.vue # 搜索历史输入
│   │   └── 📄 Tooltips.vue     # 文本提示
│   ├── 📁 directive/           # 自定义指令
│   │   ├── 📄 debounce.js      # 防抖指令
│   │   ├── 📄 dialogDrag.js    # 对话框拖拽
│   │   ├── 📄 inputFocus.js    # 输入框聚焦
│   │   ├── 📄 inputInteger.js  # 整数输入
│   │   ├── 📄 thousandSeparator.js # 千分位分隔符
│   │   └── 📄 toolTips.js      # 工具提示指令
│   ├── 📁 fetch/               # API接口管理
│   │   ├── 📁 modules/         # 模块化API
│   │   │   ├── 📄 andon.js     # 安灯盒子API
│   │   │   ├── 📄 customer.js  # 客户管理API
│   │   │   ├── 📄 dashboard.js # 驾驶舱API
│   │   │   ├── 📄 deal.js      # 合同/交付API
│   │   │   ├── 📄 develop.js   # 开发管理API
│   │   │   ├── 📄 hr.js        # 人力资源API
│   │   │   ├── 📄 inquiry.js   # 询盘API
│   │   │   ├── 📄 performance.js # 绩效API
│   │   │   ├── 📄 system.js    # 系统管理API
│   │   │   ├── 📄 team.js      # 团队管理API
│   │   │   ├── 📄 train.js     # 培训API
│   │   │   ├── 📄 travel.js    # 差旅API
│   │   │   └── 📄 view.js      # 看板API
│   │   ├── 📄 api-registry.js  # API注册
│   │   ├── 📄 cancel.js        # 请求取消
│   │   ├── 📄 config.js        # 请求配置
│   │   ├── 📄 error-handler.js # 错误处理
│   │   ├── 📄 index.js         # 入口文件
│   │   ├── 📄 interceptors.js  # 拦截器
│   │   ├── 📄 loading.js       # 加载状态
│   │   └── 📄 request.js       # 请求封装
│   ├── 📁 mixins/              # 混入文件
│   │   └── 📄 queryUsersByArid.js # 用户查询混入
│   ├── 📁 pages/               # 页面组件
│   │   ├── 📁 A-TEMPLATE/      # 页面模板
│   │   ├── 📁 andonBoxManagement/ # 安灯盒子管理
│   │   ├── 📁 braceletManagement/ # 手环管理
│   │   ├── 📁 customerManagement/ # 客户管理
│   │   ├── 📁 dashboardManagement/ # 驾驶舱管理
│   │   ├── 📁 deliveryManagement/ # 交付管理
│   │   ├── 📁 developmentManagement/ # 开发管理
│   │   ├── 📁 humanResourceManagement/ # 人力资源
│   │   ├── 📁 inquiryManagement/ # 询盘管理
│   │   ├── 📁 performanceManagement/ # 绩效管理
│   │   ├── 📁 systemManagement/ # 系统管理
│   │   ├── 📁 teamManagement/ # 团队管理
│   │   ├── 📁 threeColorLightManagement/ # 三色灯管理
│   │   ├── 📁 trainManagement/ # 培训管理
│   │   ├── 📁 travelManagement/ # 差旅管理
│   │   ├── 📁 viewManagement/ # 看板管理
│   │   ├── 📁 Z-Layout/       # 布局组件
│   │   ├── 📄 Home.vue         # 首页
│   │   ├── 📄 Login.vue        # 登录页
│   │   ├── 📄 NetBreakTip.vue  # 网络断开提示
│   │   ├── 📄 NotFound.vue     # 404页面
│   │   └── 📄 welCome.vue      # 欢迎页
│   ├── 📁 router/              # 路由配置
│   │   ├── 📁 modules/         # 路由模块
│   │   │   ├── 📄 andon.js     # 安灯盒子路由
│   │   │   ├── 📄 customer.js  # 客户管理路由
│   │   │   ├── 📄 deal.js      # 合同管理路由
│   │   │   ├── 📄 develop.js   # 开发管理路由
│   │   │   ├── 📄 hr.js        # 人力资源路由
│   │   │   ├── 📄 inquiry.js   # 询盘路由
│   │   │   ├── 📄 performance.js # 绩效路由
│   │   │   ├── 📄 system.js    # 系统管理路由
│   │   │   ├── 📄 team.js      # 团队管理路由
│   │   │   ├── 📄 train.js     # 培训路由
│   │   │   ├── 📄 travel.js    # 差旅路由
│   │   │   └── 📄 view.js      # 看板路由
│   │   ├── 📄 auth.js          # 权限验证
│   │   ├── 📄 error-handling.js # 错误处理
│   │   ├── 📄 guards.js        # 路由守卫
│   │   ├── 📄 index.js         # 路由入口
│   │   └── 📄 routes.js        # 路由配置
│   ├── 📁 styles/              # 全局样式
│   │   ├── 📄 animation.scss   # 动画样式
│   │   ├── 📄 common.scss      # 通用样式
│   │   ├── 📄 element-lightMES.scss # Element主题
│   │   ├── 📄 element-variables.scss # Element变量
│   │   ├── 📄 global.scss      # 全局样式
│   │   ├── 📄 main.scss        # 主样式入口
│   │   ├── 📄 responsive.scss  # 响应式样式
│   │   └── 📄 utils.scss       # 工具样式
│   ├── 📁 util/                # 工具函数
│   │   ├── 📁 pollerManager/   # 轮询管理器
│   │   │   ├── 📄 pollerManager.js # 轮询管理器
│   │   │   └── 📄 smartPoller.js # 智能轮询
│   │   ├── 📄 array.js         # 数组工具
│   │   ├── 📄 common.js        # 通用工具
│   │   ├── 📄 date.js          # 日期工具
│   │   ├── 📄 localStorage.js  # 本地存储
│   │   ├── 📄 math.js          # 数学工具
│   │   ├── 📄 notify.js        # 通知工具
│   │   ├── 📄 tableColumns.js  # 表格列配置
│   │   └── 📄 tool.js          # 工具函数
│   ├── 📁 vuex/                # 状态管理
│   │   ├── 📁 modules/         # 状态模块
│   │   │   ├── 📄 auth.js      # 认证状态
│   │   │   ├── 📄 com.js       # 通用状态
│   │   │   ├── 📄 inquiry.js   # 询盘状态
│   │   │   └── 📄 layout.js    # 布局状态
│   │   └── 📄 index.js         # 状态入口
│   ├── 📄 App.vue              # 根组件
│   └── 📄 main.js              # 入口文件
├── 📁 static/                  # 静态资源
├── 📄 .babelrc                 # Babel配置
├── 📄 .cursorrules             # Cursor规则
├── 📄 .editorconfig            # 编辑器配置
├── 📄 .eslintignore            # ESLint忽略
├── 📄 .eslintrc.js             # ESLint配置
├── 📄 .gitignore               # Git忽略
├── 📄 .npmrc                   # pnpm配置
├── 📄 .postcssrc.js            # PostCSS配置
├── 📄 .prettierignore          # Prettier忽略
├── 📄 .prettierrc.js           # Prettier配置
├── 📄 index.html               # HTML模板
├── 📄 package.json             # 项目配置
├── 📄 pnpm-lock.yaml           # pnpm依赖锁定文件
├── 📄 README.md                # 项目说明
└── 📄 STANDARD.md              # 编码规范
```

## 💻 开发指南

### 开发环境配置

1. **安装依赖**
   ```bash
   pnpm install
   ```

2. **启动开发服务器**
   ```bash
   pnpm dev
   ```

3. **代码检查**
   ```bash
   # ESLint检查
   <!-- pnpm lint -->
   
   # Stylelint检查
   <!-- pnpm lint:style -->
   ```

### 常用 pnpm 命令

```bash
# 安装依赖
pnpm install

# 添加依赖
pnpm add <package-name>
pnpm add -D <package-name>  # 开发依赖

# 移除依赖
pnpm remove <package-name>

# 更新依赖
pnpm update

# 运行脚本
pnpm <script-name>

# 查看依赖树
pnpm list

# 清理缓存
pnpm store prune
```

### 构建说明

```bash
# 开发环境构建
# pnpm build:dev

# 生产环境构建
pnpm build

# 生产环境构建并部署
pnpm build:deploy

# 构建并生成分析报告
# pnpm build --report
```

### 代码规范

项目遵循以下代码规范：

- **JavaScript**: ESLint + Prettier
- **CSS/SCSS**: Stylelint
- **Vue组件**: Vue官方风格指南
- **Git提交**: 约定式提交规范

## 🌐 部署说明

### 环境配置

项目支持多环境部署：

- **开发环境**: `pnpm dev`
- **生产环境**: `pnpm build`

### 部署步骤

1. **构建项目**
   ```bash
   pnpm build
   ```

2. **上传文件**
   将 `dist` 目录下的文件上传到服务器

3. **配置服务器**
   - 配置Nginx反向代理
   - 设置HTTPS证书
   - 配置缓存策略
  
4. **构建并自动部署项目**
   ```bash
   pnpm build:deploy
   ```

## 📚 API文档

项目采用Swagger生成API文档，支持RESTful风格：

| 环境 | 文档地址 | 说明 |
|------|----------|------|
| 开发环境 | https://dev.base.lightmes.cn/background/doc.html | 开发调试用 |
| 测试环境 | http://ops.test.base.lightmes.cn/background/doc.html | 测试验证用 |
| 生产环境 | http://ops.lightmes.cn/background/doc.html | 生产环境用 |

## 🌍 浏览器支持

### 推荐浏览器

- **Chrome**: >= 88.0
- **Firefox**: >= 85.0
- **Safari**: >= 14.0
- **Edge**: >= 88.0

### 兼容性说明

- ✅ 支持现代浏览器ES6+特性
- ✅ 支持CSS Grid和Flexbox布局
- ⚠️ 不支持IE浏览器
- 📱 支持移动端响应式设计

## ❓ 常见问题

### Q: 项目启动失败怎么办？
A: 请检查以下几点：
1. Node.js版本是否符合要求（>= 18.x）
2. pnpm是否已正确安装（建议版本 >= 8.x）
3. 依赖是否安装完整（删除node_modules重新安装：`pnpm install`）
4. 端口8082是否被占用

### Q: 如何修改服务器环境地址？
A: 在 `config/` 目录下修改对应环境的配置文件

### Q: 如何添加新的页面？
A: 参考现有页面结构，在 `src/pages/` 下创建新目录和组件

### Q: 如何自定义主题？
A: 修改 `src/styles/` 下的样式文件，或使用Element UI的主题定制功能

## 📄 许可证

### 版权声明

**树字OPS后台管理系统**

版权所有 (C) 深圳市树字信息科技有限公司 2023

### 使用条款

本软件为专有软件，仅限内部使用。未经授权，不得：

1. **商业使用**: 不得用于任何商业目的
2. **再分发**: 不得以任何形式分发给第三方
3. **逆向工程**: 不得进行逆向工程或反编译
4. **修改声明**: 不得删除或修改版权声明

### 免责声明

- 本软件按"原样"提供，不提供任何明示或暗示的保证
- 作者不对使用本软件造成的任何损失承担责任
- 使用本软件即表示同意上述条款

---

## 📞 联系我们

- **公司**: 深圳市树字信息科技有限公司
- **官网**: [lightmes.cn](http://www.lightmes.cn)
- **技术支持**: <EMAIL>
- **商务合作**: <EMAIL>

---

<div align="center">

** 树字MES 让制造简单些 **

Made with ❤️ by 树字MES前端团队

</div>