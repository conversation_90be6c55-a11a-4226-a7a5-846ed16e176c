# macOS 系统文件
.DS_Store

# Node 相关
node_modules/
package-lock.json
yarn.lock
pnpm-lock.yaml
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 构建产物与缓存
dist/
build/
coverage/
tmp/
temp/
.cache/
.cache-loader/
.pnpm-store
*.tsbuildinfo

# 环境变量文件
.env
.env.*
!.env.example

# 编辑器配置文件
.idea/
.vscode/
*.code-workspace
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 系统文件
Thumbs.db
ehthumbs.db
Desktop.ini

# 其他配置与临时文件
.browserslistrc
inspect.js
.cursorrules
*.log

# 测试输出
junit.xml
test-results.xml