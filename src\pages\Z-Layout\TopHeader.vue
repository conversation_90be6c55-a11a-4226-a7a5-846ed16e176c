<template>
	<el-container class="TopHeader header-container">
		<el-header>
			<div class="header-main flex-justify-between">
				<!-- 顶部左边部分 -->
				<div class="header-left flex-align-center">
					<!-- 团队logo -->
					<div class="header-img" :style="{ width: isCollapse ? '60px' : '225px' }">
						<!-- <img :src="isCollapse ? logImg : logImg2" /> -->
						<!-- <img :src="userInfos.team.teamImage || logImg" /> -->
						<img class="" :src="logImg" />
					</div>
					<!-- 折叠按钮 -->
					<div class="header-switchIcon">
						<img
							src="@/assets/img/switchIcon.svg"
							:style="{ transform: isCollapse ? 'rotate(180deg)' : '' }"
							@click="isCollapse = !isCollapse"
						/>
					</div>
					<!-- 面包屑 -->
					<el-breadcrumb class="fs-16">
						<el-breadcrumb-item v-for="crumb in breadcrumbData" :key="crumb.path">
							{{ crumb.title }}
						</el-breadcrumb-item>
					</el-breadcrumb>
				</div>
				<!-- 顶部右边部分 -->
				<div class="header-right flex-align-center gap-10">
					<!-- 在线支持工作台 -->
					<OnlineSupportBadge />
					<!-- 问答论坛 -->
					<QandABadge />
					<!-- 聊天机器人 -->
					<el-button type="text" class="m0" @click="$store.commit('setShowChatBot', !showChatBot)">DeepSeek</el-button>
					<!-- 菜单搜索 -->
					<SearchMenu />
					<!-- 全屏 -->
					<i class="el-icon-full-screen pointer fs-24 color-666" @click="fullScreen"></i>
					<!-- 用户头像 -->
					<!-- <img :src="userInfo.photo ? userInfo?.photo : userImg" class="user-img" /> -->
					<img :src="userImg" @error="e => (e.target.src = userImg)" class="user-img" />
					<!-- 功能区 -->
					<el-dropdown trigger="click" :hide-on-click="false">
						<el-button class="el-dropdown-link fs-16" type="text">
							<span class="user-name">{{ userInfo.userName }}</span>
							<i class="el-icon-caret-bottom color-555"></i>
						</el-button>
						<el-dropdown-menu class="user-dropdown-menu" slot="dropdown">
							<!-- 本部座位图 -->
							<el-popover v-if="showCompanyMap" placement="left" trigger="hover">
								<el-image fit="scale-down" class="border ml10" :src="companyMap" :preview-src-list="[companyMap]"> </el-image>
								<el-dropdown-item slot="reference" class="el-icon-map-location"> 座位图 </el-dropdown-item>
							</el-popover>

							<!-- 业务地图 -->
							<el-dropdown-item v-if="showBusinessMap" class="el-icon-data-line" @click.native="goTo('/BusinessMap')">
								业务地图
							</el-dropdown-item>

							<!-- 常用工具 -->
							<el-popover placement="left" trigger="hover">
								<div class="flex-column gap-10">
									<div class="el-icon-set-up fs-14 bolder"> 常用工具（内部学习和交流使用，点击下载）</div>
									<div>
										<div class="hover-green el-icon-printer" @click="downloadFile('Print Plugin.zip')"> CLodop 打印工具</div>
										<div class="ml10 fs-12 color-999">（MES/WMS系统打印标签时需用到，含安装包和使用说明）</div>
									</div>
									<div>
										<div class="hover-green el-icon-crop" @click="downloadFile('Snipaste.zip')"> Snipaste 截图/贴图工具</div>
										<div class="ml10 fs-12 color-999">（简单但强大的贴图工具，下载解压后执行.exe即可）</div>
									</div>
									<div>
										<div class="hover-green el-icon-help" @click="downloadFile('ChromeSetup.zip')"> Chrome 谷歌浏览器</div>
										<div class="ml10 fs-12 color-999">（离线安装包，为兼容win7的较低版本）</div>
									</div>
								</div>
								<el-dropdown-item slot="reference">
									<div class="el-icon-set-up h-30 flex-align-center gap-5 pt3 pb3"> 常用工具</div>
								</el-dropdown-item>
							</el-popover>

							<!-- 修改密码 -->
							<el-dropdown-item class="el-icon-edit-outline" @click.native="dialogPsw = true"> 修改密码 </el-dropdown-item>
							<!-- 退出登录 -->
							<el-dropdown-item class="icon-third_tuichudenglu" @click.native="logout()">退出登录</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown>
				</div>
			</div>
		</el-header>

		<!-- 修改密码弹窗 -->
		<el-dialog :visible.sync="dialogPsw" width="500px" :close-on-click-modal="false" @close="closeDialog">
			<el-row slot="title">修改密码</el-row>
			<el-form label-width="100px" label-position="left">
				<el-form-item label="新密码">
					<el-input v-model="pswForm.confirmPassword" placeholder="输入新密码" show-password></el-input>
				</el-form-item>
				<el-form-item label="确认密码">
					<el-input v-model="pswForm.newPassword" placeholder="确认新密码" show-password></el-input>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="savePsw">保存</el-button>
			</el-row>
		</el-dialog>
	</el-container>
</template>
<script>
import { mapGetters } from 'vuex';
import userImg from '@/assets/img/user_defaut.webp';
import logImg from '@/assets/img/lightmes-logo.webp';
// import logImg2 from '@/assets/img/lightmes-logo.svg';
import companyMap from '@/assets/img/company-map.webp';

import SearchMenu from './SearchMenu.vue';
import OnlineSupportBadge from './Badge/OnlineSupportBadge.vue';
import QandABadge from './Badge/QandABadge.vue';

export default {
	name: 'TopHeader', //该组件主要用于展示顶部LOGO、面包屑、用户信息和切换团队、退出登录、修改密码等
	components: { SearchMenu, OnlineSupportBadge, QandABadge },
	data() {
		return {
			userImg, //用户图片默认
			logImg, //菜单上面的logo
			companyMap, //公司座位图
			// logImg2, //菜单上面折叠后的小logo
			teamInfoList: [], //团队清单
			screenWidth: 1920, //默认屏幕宽度
			isCollapse: false, //折叠控制
			dialogPsw: false, //修改密码弹窗
			//修改密码表单
			pswForm: {
				confirmPassword: '',
				newPassword: '',
			},
			fullScreenFlag: false,
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos', 'userInfo', 'userRoles']), //当前登录用户信息（含团队等）
		...mapGetters(['pswSimple']),
		...mapGetters(['showChatBot']),
		//面包屑数据
		breadcrumbData() {
			// $route.matched 返回了一个路由嵌套层级的数组，考虑到以后会多级路由嵌套的可能，因此面包屑用$route.matched返回的数据
			const matchedRoutes = this.$route.matched.slice(1); //去掉首页
			const breadcrumbs = [];

			for (const route of matchedRoutes) {
				if (route.meta && route.meta.title) {
					breadcrumbs.push({ title: route.meta.parentTitle }, { title: route.meta.title, path: route.path });
				}
			}

			return breadcrumbs;
		},
		// 显示本部地图（用户名以6开头/超级管理员）
		showCompanyMap() {
			if (this.userRoles?.some(item => item.arid == 1 || item.roleName == '超级管理员')) return true;
			return this.userInfo.userName.startsWith('6');
		},
		// 显示业务地图（用户名以6和8开头/超级管理员/departmentName = 树字工厂）
		showBusinessMap() {
			if (this.userInfo.departmentName == '树字工厂') return true;
			if (this.userRoles?.some(item => item.arid == 1 || item.roleName == '超级管理员')) return true;
			return this.userInfo.userName.startsWith('6') || this.userInfo.userName.startsWith('8');
		},
	},
	// 监控data中的数据变化
	watch: {
		// 菜单根据屏幕宽度折叠菜单
		screenWidth(newval) {
			//一般笔记本的宽度
			if (newval <= 1440) {
				this.isCollapse = true;
			} else {
				this.isCollapse = false;
			}
		},
		// 监听折叠变量传递与菜单组件同步折叠状态
		isCollapse(newval) {
			this.$store.commit('setIsCollapse', newval);
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		// this.queryUserTeamData();
		//新用户初次登陆需要修改默认密码
		// if (this.pswSimple == 1) {
		// 	this.$message.warning('您的密码不安全，请及时修改密码。');
		// 	this.dialogPsw = true;
		// }
		window.removeEventListener('resize', this.handleResize);
		window.addEventListener('resize', this.handleResize);
		this.screenWidth = document.documentElement.clientWidth; //窗口宽度
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {
		window.removeEventListener('resize', this.handleResize);
	},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 路由跳转
		goTo(path) {
			const queryId = this.$route.query.queryId || this.userInfo.phoneNo;
			this.$router.push({ path, query: { queryId } });
		},
		// 全屏
		fullScreen() {
			this.fullScreenFlag = !this.fullScreenFlag;
			this.$emit('fullScreen', this.fullScreenFlag);
		},
		// 下载打印控件
		downloadFile(fileName) {
			const URL = process.env.NODE_ENV == 'production' ? '' : process.env.API_HOST;
			window.open(URL + 'template/' + fileName, '_self');
		},
		// 定义窗口大小变更通知事件
		handleResize() {
			this.screenWidth = document.documentElement.clientWidth; //窗口宽度
		},
		// 查询当前用户所有团队（这里不需要）
		queryUserTeamData() {
			//
		},
		// 切换团队（这里不需要）
		changeTeam() {
			//
		},
		// 保存密码
		async savePsw() {
			const { confirmPassword, newPassword } = this.pswForm;

			if (confirmPassword == '') {
				return this.$message.warning('请输入新密码');
			}
			if (newPassword == '') {
				return this.$message.warning('请确认新密码');
			}
			if (confirmPassword !== newPassword) {
				return this.$message.warning('两次输入不一致');
			}
			try {
				const res = await this.$axios.updatePassWordByOld(JSON.stringify({ confirmPassword, newPassword }));
				if (res.data.success) {
					this.$succ('修改成功，请重新登录！');
					this.$store.commit('setpswSimple', 0);
					this.closeDialog();
					setTimeout(() => {
						window.location.href = '/#/Login'; //一秒后回到登录页面，重新登录
					}, 1000);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.log('updatePassWordByOld |' + error);
			}
		},
		// 关闭弹窗
		closeDialog() {
			if (this.pswSimple) {
				this.$message.warning('当前初始密码不安全,请修改密码后再进行操作');
				this.dialogPsw = true;
				return;
			}
			this.dialogPsw = false;
			this.pswForm.confirmPassword = '';
			this.pswForm.newPassword = '';
		},
		// 退出登录
		logout() {
			this.$confirm('确定退出系统吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					window?.localStorage.clear();
					window?.sessionStorage.clear();
					this.$store.commit('resetState'); // 清除vuex的数据
					window.location.href = '/#/Login';
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
	},
};
</script>

<style lang="scss">
//顶部 header样式
.header-container {
	box-sizing: border-box;
	// position: absolute;
	// top: 0;
	width: 100%;

	.el-header {
		padding: 0;
		box-sizing: border-box;
		border-bottom: 1px solid rgb(0, 191, 191);

		.header-main {
			width: 100%;
			height: 60px;
			box-sizing: border-box;
			padding: 0 10px 0 2px;

			.header-left {
				.header-img {
					width: 225px;
					overflow: hidden;
					// padding: 1px;

					img {
						padding-top: 4px;
						height: 60px;
						max-width: 220px;
					}
				}

				.header-switchIcon {
					margin: 0 2vw 0 0.5vw;
					height: 20px;
					overflow: hidden;

					img {
						cursor: pointer;
					}
				}
			}

			.header-right {
				.green {
					color: #1e9d6f !important;
				}
				.user-img {
					vertical-align: middle;
					width: 36px;
					height: 36px;
					border-radius: 18px;
				}

				.user-name {
					color: #303133;
					font-weight: normal;
				}
			}
		}
	}
}

.user-dropdown-menu.el-dropdown-menu {
	overflow: auto;
	max-height: 50vh;
	color: rgba(255, 255, 255, 0.8);
	.el-dropdown-menu__item {
		display: flex;
		align-items: center;
		gap: 5px;
	}
}
</style>
