<template>
	<div id="documentaryDaily" :class="isMoveCom ? (openMove ? 'moveToggle' : 'moveToggle moveToggle-hide') : ''">
		<BaseLayout>
			<template #header>
				<span class="search-label">周次</span>
				<SwitchDatePicker
					class="w-220"
					v-model="searchForm.week"
					type="week"
					value-format=""
					format="yyyy 年 第 W 周"
					placeholder="请选择周次"
					:clearable="false"
					@change="queryTableData"
				/>
				<SearchHistoryInput
					width="120"
					name="salesmanName"
					placeholder="业务顾问"
					v-model.trim="searchForm.saleName"
					@input="queryTableData(1)"
				/>
				<el-checkbox v-model="searchForm.showAllPlan" @change="queryTableData">查看过期预约</el-checkbox>
				<el-checkbox v-model="searchForm.selectRange" :true-label="1" :false-label="0" @change="queryTableData">
					仅看自己
				</el-checkbox>
				<el-button type="text" class="el-icon-refresh-right ml-auto" @click="queryTableData('refresh')">刷新</el-button>
				<el-button v-if="isMoveCom" type="text" class="el-icon-arrow-left" @click.stop="openMove = false">返回</el-button>
			</template>
			<template #main>
				<div class="table-wrapper">
					<table class="table-main-daily" cellpadding="5" cellspacing="0">
						<tr class="sticky-top">
							<th class="W5 text-center">序号</th>
							<th class="W10 text-center">业务顾问</th>
							<th class="W10 text-center" v-for="(day, index) in weekDays" :key="index">
								{{ `周${day} (${getWeekDate(index + 1, searchForm.week)})` }}
							</th>
						</tr>
						<!-- 表格内容 -->
						<tr v-for="(item, index) in week" :key="index">
							<td class="text-center">{{ index + 1 }}</td>
							<td>{{ item.salesman }}</td>
							<td>
								<div v-for="(day, mIndex) in item.monday" :key="mIndex">
									<span v-for="(obj, dIndex) in day" :key="dIndex">
										<span
											v-for="(val, key, oIndex) in obj"
											:key="oIndex"
											class="hover-green"
											:style="{ color: getTextColor(val) }"
											@click="openDetail(key)"
										>
											<Tooltips :cont-str="val" />
										</span>
									</span>
								</div>
							</td>
							<td>
								<div v-for="(day, tIndex) in item.tuesday" :key="tIndex">
									<span v-for="(obj, dIndex) in day" :key="dIndex">
										<span
											v-for="(val, key, oIndex) in obj"
											:key="oIndex"
											class="hover-green"
											:style="{ color: getTextColor(val) }"
											@click="openDetail(key)"
										>
											<Tooltips :cont-str="val" />
										</span>
									</span>
								</div>
							</td>
							<td class="ellipsis">
								<div v-for="(day, wIndex) in item.wednesday" :key="wIndex">
									<span v-for="(obj, dIndex) in day" :key="dIndex">
										<span
											v-for="(val, key, oIndex) in obj"
											:key="oIndex"
											class="hover-green"
											:style="{ color: getTextColor(val) }"
											@click="openDetail(key)"
										>
											<Tooltips :cont-str="val" />
										</span>
									</span>
								</div>
							</td>
							<td>
								<div v-for="(day, tIndex) in item.thursday" :key="tIndex">
									<span v-for="(obj, dIndex) in day" :key="dIndex">
										<span
											v-for="(val, key, oIndex) in obj"
											:key="oIndex"
											class="hover-green"
											:style="{ color: getTextColor(val) }"
											@click="openDetail(key)"
										>
											<Tooltips :cont-str="val" />
										</span>
									</span>
								</div>
							</td>
							<td>
								<div v-for="(day, fIndex) in item.friday" :key="fIndex">
									<span v-for="(obj, dIndex) in day" :key="dIndex">
										<span
											v-for="(val, key, oIndex) in obj"
											:key="oIndex"
											class="hover-green"
											:style="{ color: getTextColor(val) }"
											@click="openDetail(key)"
										>
											<Tooltips :cont-str="val" />
										</span>
									</span>
								</div>
							</td>
							<td>
								<div v-for="(day, sIndex) in item.saturday" :key="sIndex">
									<span v-for="(obj, dIndex) in day" :key="dIndex">
										<span
											v-for="(val, key, oIndex) in obj"
											:key="oIndex"
											class="hover-green"
											:style="{ color: getTextColor(val) }"
											@click="openDetail(key)"
										>
											<Tooltips :cont-str="val" />
										</span>
									</span>
								</div>
							</td>
							<td>
								<div v-for="(day, sIndex) in item.sunday" :key="sIndex">
									<span v-for="(obj, dIndex) in day" :key="dIndex">
										<span
											v-for="(val, key, oIndex) in obj"
											:key="oIndex"
											class="hover-green"
											:style="{ color: getTextColor(val) }"
											@click="openDetail(key)"
										>
											<Tooltips style="" :cont-str="val" />
										</span>
									</span>
								</div>
							</td>
						</tr>
					</table>
				</div>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import { debounce, dateFormat } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import SwitchDatePicker from '@/components/DateSelect/SwitchDatePicker.vue';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
export default {
	props: {
		twidList: Array,
		channelName: Array,
		isMoveCom: Boolean, // 将该组件设为可移动组件
	},
	// import引入的组件需要注入到对象中才能使用
	components: {
		SwitchDatePicker,
	},
	name: 'documentaryDaily',
	data() {
		return {
			// 搜索条件
			searchForm: {
				week: new Date().getTime(),
				showAllPlan: false, //查询日期范围
				selectRange: 1, //查询人员范围
				endTime: '',
				startTime: '',
				saleName: '',
			},

			nextPlan: [],
			week: [],

			colorList: {
				'(约)': 'salmon',
				BZ: 'salmon',
				BH: 'dodgerblue',
				BW: 'mediumpurple',
			},

			openMove: false,
			weekDays: ['一', '二', '三', '四', '五', '六', '日'],
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {
		channelName() {
			this.queryTableData(1);
		},
		twidList() {
			this.queryTableData();
		},
		openMove(newVal) {
			if (!newVal) {
				this.$emit('closeMove');
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		// this.searchForm = JSON.parse(window?.localStorage.getItem(this.$options.name + '_searchForm')) || this.searchForm;
		console.log('searchForm', this.searchForm);
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 获取每周日期
		getWeekDate(index, week) {
			return this.$moment(week)
				.startOf('isoWeek')
				.add(index - 1, 'day')
				.format('MM/DD');
		},

		// 跟单日报数据
		queryTableData: debounce(function (type) {
			this.searchForm.startTime = this.$moment(this.searchForm.week).startOf('isoWeek').valueOf();
			this.searchForm.endTime = this.$moment(this.searchForm.week).endOf('isoWeek').valueOf();
			// window?.localStorage.setItem(this.$options.name + '_searchForm', JSON.stringify(this.searchForm));
			this.$axios
				.selectMerchandiserDaily(JSON.stringify({ ...this.searchForm, twidList: this.twidList, channelName: this.channelName }))
				.then(res => {
					if (res.data.success) {
						this.week = [];
						res.data.data.forEach(item => {
							const obj = {
								salesman: '',
								auid: '',
								friday: [],
								monday: [],
								saturday: [],
								sunday: [],
								thursday: [],
								tuesday: [],
								wednesday: [],
							};
							const weekMap = {
								0: 'sunday',
								1: 'monday',
								2: 'tuesday',
								3: 'wednesday',
								4: 'thursday',
								5: 'friday',
								6: 'saturday',
							};
							if (!obj.salesman.includes(item.salesman)) {
								obj.salesman = item.salesman;
								obj.auid = item.auid;
							}
							if (item.weekMerchandiserDailyVOList) {
								item.weekMerchandiserDailyVOList.forEach(val => {
									const weekNo = new Date(val.documentaryTime).getDay();
									const idid = val.drid;
									const content = val.contentJoint;
									obj[weekMap[weekNo]].push([{ [idid]: content }]);
								});
							}
							this.week.push(obj);
						});
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');

						this.queryNextPlan();
						type == 'openMove' && (this.openMove = true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectMerchandiserDaily |' + error);
				});
		}),
		// 跟单计划数据
		queryNextPlan() {
			this.nextPlan = [];
			let startTime;
			if (!this.searchForm.showAllPlan) {
				// 不查看过期预约时
				const nowTime = new Date(new Date().toLocaleDateString()).getTime(); //当前时间
				if (this.searchForm.endTime <= nowTime) {
					// 如果是过去的时间时（过去周）将预约信息清空且不再查询
					this.nextPlan = [];
					return;
				} else if (this.searchForm.startTime >= nowTime) {
					// 如果是未来时间时（未来周）等于这未来周周一即与查询跟单记录时间一致
					startTime = this.searchForm.startTime;
				} else {
					startTime = nowTime; //如果是本周则为当前时间
				}
			} else {
				// 查看过去的预约时间与查询跟单记录时间一致
				startTime = this.searchForm.startTime;
			}
			// this.dateEnd,
			this.$axios
				.selectNextPlan(
					JSON.stringify({
						...this.searchForm,
						startTime,
						twidList: this.twidList || [],
						channelName: this.channelName || [],
					}),
				)
				.then(res => {
					if (res.data.success) {
						this.nextPlan = res.data.data;
						this.week.forEach(item => {
							this.nextPlan.forEach(plan => {
								if (plan.salesman == item.auid) {
									plan.documentaryDailyVOList.forEach(day => {
										item.monday.unshift(day.monday);
										item.friday.unshift(day.friday);
										item.saturday.unshift(day.saturday);
										item.sunday.unshift(day.sunday);
										item.thursday.unshift(day.thursday);
										item.wednesday.unshift(day.wednesday);
										item.tuesday.unshift(day.tuesday);
									});
								}
							});
						});
						// console.log("nextPlan", this.nextPlan);
						// console.log("newWeek", this.week);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectNextPlan |' + error);
				});
		},
		// 查询跟单详情数据
		openDetail(idid) {
			// this.$refs.InquiryDetail.showDetailCom('修改', { idid }, 'selectInquiryDocumentary');
			this.$emit('openDetail', '修改', { idid }, 'selectInquiryDocumentary');
		},
		// 判断文本                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            颜色
		getTextColor(str) {
			let color;
			// color = colorList[str];
			if (str.includes('(约)')) {
				color = 'salmon';
			}
			if (str.includes('BZ')) {
				color = 'darkturquoise';
			}
			if (str.includes('BH')) {
				color = 'dodgerblue';
			}
			if (str.includes('BW')) {
				color = 'mediumpurple';
			}
			return color;
		},

		dateFormat, //日期format
	},
};
</script>

<style lang="scss" scoped>
#documentaryDaily {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
	:deep(.el-checkbox__label) {
		color: red;
	}

	&.moveToggle {
		.table-wrapper {
			height: calc(100vh - 205px) !important;
		}
		@media screen and (max-width: 1280px) {
			.table-wrapper {
				height: calc(100vh - 150px) !important;
			}
		}
	}

	.table-wrapper {
		height: calc(100vh - 250px);
	}
	@media screen and (max-width: 1280px) {
		.table-wrapper {
			height: calc(100vh - 200px) !important;
		}
	}
	.table-main-daily {
		width: 100%;
		color: #606266;
		border-left: 1px solid #e9e9e9;
		border-bottom: 1px solid #e9e9e9;

		.sticky-top {
			position: sticky;
			top: -10px;
		}
		hover-green {
			&:hover {
				color: #28d094;
				text-decoration: underline;
				cursor: pointer;
			}
		}

		tr {
			th {
				text-align: left;
				font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
				font-weight: 650;
				font-size: 14px;
				color: #666666;
				padding: 15px 0 15px 5px;
				background: #f5f5f5;
				border-right: 1px solid #e9e9e9;
				border-top: 1px solid #e9e9e9;
				word-wrap: break-word;
			}

			td {
				border-right: 1px solid #e9e9e9;
				border-top: 1px solid #e9e9e9;
				font-size: 12px;
				vertical-align: top;
				max-width: 10vw;
			}
		}
	}
}
</style>
