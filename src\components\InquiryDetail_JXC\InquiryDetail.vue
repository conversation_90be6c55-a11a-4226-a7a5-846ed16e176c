<!-- 询盘详情（聚心城专用） -->
<template>
	<div :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper" tabindex="-1">
			<!-- 头部标题 -->
			<div class="detail-head">
				<div>
					<span v-if="isPublicInquiry" class="detail-title">
						<span>查看公海询盘</span>
						<span v-if="detailForm.oldConsultantConsultName" class="fs-12 grenn">
							原咨询人：{{ detailForm.oldConsultantConsultName }}
						</span>
						<span v-if="detailForm.oldConsultant" class="fs-12 grenn">原业务员：{{ detailForm.oldConsultant }}</span>
					</span>
					<span v-else class="detail-title">{{ titleName }}询盘</span>
					<!-- 已备案询盘可生成复购询盘 -->
					<el-tooltip v-if="detailForm.status === 0 && !isPublicInquiry" placement="top">
						<div slot="content" class="max-w-250 p5">
							<p>复购说明如下：</p>
							<p>点击复购后将会生成一个新的复购询盘，并同步当前【基本信息、咨询情况、业务反馈】的内容</p>
							<pre>公司名：{{ detailForm.registeredBusinessName }}</pre>
							<pre>备案号：{{ detailForm.clientNo }}</pre>
						</div>
						<el-button type="primary" size="mini" @click="saveRePurchase">复购</el-button>
					</el-tooltip>
				</div>
				<!-- 其他功能 -->
				<div class="flex-align-center gap-10">
					<el-button v-if="isPublicInquiry" type="primary" size="mini" @click="saveEdit('public')">领取</el-button>
					<!-- 展开/收起 -->
					<el-button v-show="titleName !== '添加'" type="text" @click="isFoldAll = !isFoldAll">
						{{ isFoldAll ? '全部展开' : '全部收起' }}
					</el-button>
					<!-- 翻页 -->
					<div class="flex-align-center gap-10" v-if="showPage">
						<el-button
							type="text"
							:disabled="nowIndex == 0"
							@click="
								nowIndex > 0 ? --nowIndex : '';
								queryDetailData(filteredInquiryOptions[nowIndex], 'change');
							"
						>
							上一页</el-button
						>
						<el-select
							v-model="detailForm.idid"
							placeholder="请选择"
							size="mini"
							filterable
							@change="queryDetailData(detailForm, 'change')"
						>
							<el-option
								v-for="(item, index) in filteredInquiryOptions"
								:key="index"
								:label="jointString(' ', item.number || item.idNumber, item.companyName)"
								:value="item.idid"
							>
								{{
									jointString(
										' ',
										item.number || item.idNumber,
										item.companyName || item.registeredBusinessName || item.tripClientName,
										item.customerName,
									)
								}}
							</el-option>
						</el-select>
						<el-button
							type="text"
							:disabled="nowIndex == filteredInquiryOptions.length - 1"
							@click="
								nowIndex < filteredInquiryOptions.length - 1 ? ++nowIndex : '';
								queryDetailData(filteredInquiryOptions[nowIndex], 'change');
							"
							>下一页
						</el-button>
					</div>
					<el-button type="text" class="el-icon-arrow-left" @click="closeDetailCom">返回</el-button>
				</div>
			</div>
			<!-- 主体内容 -->
			<div class="detail-content">
				<!-- 基本信息表单 -->
				<div class="basic-info">
					<p class="detail-content-title pointer" @click="titleName != '添加' ? (isFold.basic = !isFold.basic) : ''">
						<span class="mr10">基本信息</span>
						<i :class="[isFold.basic ? 'el-icon-arrow-right' : 'el-icon-arrow-down']"></i>
					</p>
					<transition name="fold-transition" mode="out-in">
						<div v-show="!isFold.basic" class="overflow-auto">
							<table class="base-table" cellpadding="5" cellspacing="0">
								<tr>
									<th class="label-required W10">日期时间</th>
									<th class="label-required W10">客户称呼</th>
									<th class="label-required W10">来源</th>
									<th class="W10">产品</th>
									<th class="label-required W10">联系方式</th>
									<th class="label-required W15">地区</th>
									<th colspan="2" class="W25">
										<div class="flex-align-center">
											<span class="mr10">公司工商注册名称</span>
											<!-- 已备案时显示备案号，未备案时显示备案按钮，复购询盘不需要备案 -->
											<el-button
												size="mini"
												type="primary"
												v-show="titleName != '添加' && detailForm.status == 1 && detailForm.registeredBusinessName"
												class="p3 w-50"
												@click="saveEdit('record')"
												>备案</el-button
											>
											<span v-show="titleName != '添加' && detailForm.status == 0" class="primary">
												备案号：{{ detailForm.clientNo }}
											</span>
										</div>

										<div v-if="showTips && detailForm.status !== 0" class="red fs-12">
											* 当前名称存在空格可能会导致备案信息不准确，若非特殊情况请核对后再备案！
										</div>

										<!-- 展期相关内容 ：有效且在展期 spreadStatus 0无展期 1有展期 2展期审核中 3展期审核不同意 4永久保护-->
										<div v-if="spreadForm.spreadStatus > 0 && spreadForm.spreadStatus < 4" class="red fs-12">
											<span> 备案有效期剩余 {{ spreadForm.surplusValidDay }} 天</span>
											<el-button type="text" size="small" @click="dialogSpread = true">
												{{ { 1: '申请展期', 2: '审核中...', 3: '展期审核不通过' }[spreadForm.spreadStatus] }}
											</el-button>
										</div>
										<span v-else-if="spreadForm.status === 1" class="red fs-12">备案已失效</span>
									</th>
									<th class="W10">公司简称</th>
								</tr>
								<!-- 首行输入 -->
								<tr class="input-border-none">
									<td>
										<el-date-picker
											:disabled="isPublicInquiry"
											class="w-180"
											v-model="detailForm.idDateTime"
											value-format="timestamp"
											type="datetime"
											placeholder="请选择"
											format="yyyy-MM-dd HH:mm"
											:clearable="false"
											:default-value="new Date()"
										></el-date-picker>
									</td>
									<td>
										<el-input :disabled="isPublicInquiry" v-model="detailForm.customerName" placeholder="客户称呼"></el-input>
									</td>
									<td>
										<el-select
											:disabled="isPublicInquiry"
											v-model="detailForm.channel"
											placeholder="询盘来源"
											popper-class="select-column-3"
											clearable
											filterable
										>
											<el-option
												v-for="item in sourceList"
												:key="item.value"
												:label="item.label"
												:value="item.value"
												:disabled="item.label == '复购' && detailForm.status == 0"
												:title="item.label"
											>
												<!-- 已备案询盘不可将来源设置为复购 -->
											</el-option>
										</el-select>
									</td>
									<td>
										<el-select
											class="w-200"
											:disabled="isPublicInquiry"
											v-model="detailForm.keyword"
											placeholder="请选择产品"
											popper-class="select-column-3"
											clearable
											filterable
											multiple
											collapse-tags
										>
											<el-option v-for="item in productOptions" :key="item" :label="item" :value="item" :title="item">
											</el-option>
										</el-select>
									</td>
									<td>
										<el-input
											:disabled="isPublicInquiry"
											class="w-150"
											type="text"
											v-model="detailForm.contactInfo"
											clearable
											placeholder="请输入联系方式"
											@change="checkContactInfo(detailForm)"
										></el-input>
									</td>
									<td>
										<el-cascader
											:disabled="isPublicInquiry"
											class="W100 min-w-150"
											filterable
											:options="regionData"
											v-model="regionArray"
											placeholder="请选择地区"
										>
										</el-cascader>
									</td>
									<td colspan="2">
										<div class="flex">
											<el-input
												:disabled="isPublicInquiry || !isEditRegisteredName"
												v-model="detailForm.registeredBusinessName"
												@change="checkRegistered(detailForm)"
												placeholder="请输入该公司工商注册名"
											></el-input>
											<div class="flex-column">
												<el-button
													v-show="detailForm.registeredBusinessName"
													title="复制公司名称"
													type="text"
													size="mini"
													class="el-icon-document-copy p0"
													@click="copyToClipboard(detailForm.registeredBusinessName)"
												>
												</el-button>
												<el-button
													v-show="detailForm.registeredBusinessName"
													title="打开爱企查"
													type="text"
													size="mini"
													class="el-icon-search p0 m0 mt10"
													@click="openUrl(detailForm.registeredBusinessName)"
												>
												</el-button>
											</div>
										</div>
									</td>
									<td>
										<div class="flex">
											<el-input
												:disabled="isPublicInquiry"
												:maxlength="6"
												v-model="detailForm.companyName"
												placeholder="请输入公司简称"
											></el-input>
											<div class="flex-column">
												<el-button
													v-show="detailForm.companyName"
													title="复制公司名称"
													type="text"
													size="mini"
													class="el-icon-document-copy p0"
													@click="copyToClipboard(detailForm.companyName)"
												>
												</el-button>
												<el-button
													v-show="detailForm.companyName"
													title="打开爱企查"
													type="text"
													size="mini"
													class="el-icon-search p0 m0 mt10"
													@click="openUrl(detailForm.companyName)"
												>
												</el-button>
											</div>
										</div>
									</td>
								</tr>

								<!-- 次行标题 -->
								<tr>
									<th colspan="3" class="label-required">客户咨询情况</th>
									<th class="W10">所属行业</th>
									<th class="label-required W10">分销/代理</th>
									<th class="W10">咨询人员</th>
									<th class="W10">业务顾问</th>
									<th class="W10">营销分管</th>
									<th class="W10">BPUL</th>
								</tr>
								<!-- 次行输入 -->
								<tr class="input-border-none">
									<td colspan="3">
										<el-input
											:disabled="isPublicInquiry"
											class="mr20"
											type="textarea"
											:autosize="{ minRows: 4, maxRows: 6 }"
											v-model="detailForm.consultingCase"
											placeholder="请输入咨询情况..."
										></el-input>
									</td>
									<td>
										<el-select
											:disabled="isPublicInquiry"
											class="W100 min-w-150"
											v-model="detailForm.industry"
											placeholder="请选择所属行业"
											popper-class="select-column-4"
											clearable
											filterable
											multiple
											collapse-tags
										>
											<el-option v-for="item in industryOptions" :key="item" :label="item" :value="item" :title="item.item">
											</el-option>
										</el-select>
										<el-input
											:class="!detailForm.industryRemark && detailForm.industry?.includes('其他行业') ? 'input-border-red' : ''"
											v-model="detailForm.industryRemark"
											placeholder="请输入行业备注"
											clearable
										></el-input>
									</td>
									<td>
										<el-select
											:disabled="isPublicInquiry"
											v-model="detailForm.twid"
											placeholder="分销/代理"
											popper-class="select-column-3"
											clearable
											filterable
											@change="querysSalesmanList"
										>
											<el-option
												v-for="item in teamWorkList"
												:key="item.twid"
												:label="item.twName"
												:value="item.twid"
												:title="item.twName"
											>
											</el-option>
										</el-select>
									</td>
									<td>
										<el-select
											:disabled="isConsultLocked || isPublicInquiry"
											v-model="detailForm.consultUid"
											placeholder="咨询人员"
											popper-class="select-column-3"
											clearable
											filterable
										>
											<el-option disabled v-if="!detailForm.twid" value="">
												<span class="orange">请选择对应的分销/代理后再操作！</span>
											</el-option>
											<el-option disabled v-else-if="detailForm.twid && consultList.length == 0" value="">
												<span class="orange">当前分销/代理未与系统用户绑定，请联系管理员绑定用户后再操作！</span>
											</el-option>
											<el-option
												v-for="item in consultList"
												:key="item.auid"
												:label="item.userName"
												:value="item.auid"
												:title="item.userName"
											>
											</el-option>
										</el-select>

										<div v-show="detailForm.twid && !detailForm.consultUid" class="fs-12 red ml10"> * 未填写时将自动分配</div>
									</td>
									<td>
										<el-select
											:disabled="isPublicInquiry"
											v-model="detailForm.salesmanUid"
											placeholder="请选择业务顾问"
											@change="changeSalesman($event, 'change')"
											popper-class="select-column-3"
											clearable
											filterable
										>
											<el-option disabled v-if="!detailForm.twid" value="">
												<span class="orange">请选择对应的分销/代理后再操作！</span>
											</el-option>
											<el-option disabled v-else-if="detailForm.twid && salesmanList.length == 0" value="">
												<span class="orange">当前分销/代理未与系统用户绑定，请联系管理员绑定用户后再操作！</span>
											</el-option>
											<el-option
												v-for="item in salesmanList"
												:key="item.auid"
												:label="item.userName"
												:value="item.auid"
												:title="item.userName"
											>
											</el-option>
										</el-select>
										<el-select
											v-if="showHoster"
											:disabled="isPublicInquiry"
											v-show="hosterList.length >= 1"
											v-model="detailForm.hosterTwid"
											placeholder="请选择托管方"
											clearable
											filterable
										>
											<el-option v-for="item in hosterList" :key="item.twid" :label="item.teamworkName" :value="item.twid">
											</el-option>
										</el-select>

										<div v-show="detailForm.twid && !detailForm.salesmanUid" class="fs-12 red ml10"> * 未填写时将自动分配</div>
									</td>
									<td>
										<el-select
											:disabled="isPublicInquiry"
											v-model="detailForm.marketingDivision"
											placeholder="营销分管"
											popper-class="select-column-3"
											clearable
											filterable
										>
											<el-option disabled v-if="!detailForm.twid" value="">
												<span class="orange">请选择对应的分销/代理后再操作！</span>
											</el-option>
											<el-option disabled v-else-if="detailForm.twid && marketingList.length == 0" value="">
												<span class="orange">当前分销/代理未与系统用户绑定，请联系管理员绑定用户后再操作！</span>
											</el-option>
											<el-option
												v-for="item in marketingList"
												:key="item.auid"
												:label="item.userName"
												:value="item.auid"
												:title="item.userName"
											>
											</el-option>
										</el-select>
									</td>
									<td>
										<el-select
											:disabled="isPublicInquiry"
											v-model="detailForm.businessPartnerUnitUid"
											placeholder="BPUL"
											popper-class="select-column-3"
											clearable
											filterable
										>
											<el-option disabled v-if="!detailForm.twid" value="">
												<span class="orange">请选择对应的分销/代理后再操作！</span>
											</el-option>
											<el-option disabled v-else-if="detailForm.twid && BPUList.length == 0" value="">
												<span class="orange">当前分销/代理未与系统用户绑定，请联系管理员绑定用户后再操作！</span>
											</el-option>
											<el-option
												v-for="item in BPUList"
												:key="item.auid"
												:label="item.userName"
												:value="item.auid"
												:title="item.userName"
											>
											</el-option>
										</el-select>
									</td>
								</tr>
							</table>
							<div class="bottom-button flex">
								<div class="flex-align-center mr-auto">
									<el-checkbox :disabled="isPublicInquiry" :true-label="1" :false-label="0" v-model="detailForm.isProxy">
										是否代理
									</el-checkbox>
									<el-tooltip>
										<div slot="content">
											<div v-if="!detailForm.rePurchase">
												1.如需创建复购询盘请从已备案的询盘点击复购,创建后会同步备案信息。
												<br />
												2.只有备案过的询盘才能创建复购询盘。
											</div>

											<div v-else>该询盘为复购询盘！</div>
										</div>
										<el-checkbox disabled :true-label="1" :false-label="0" v-model="detailForm.rePurchase">
											是否复购
										</el-checkbox>
									</el-tooltip>
									<div v-if="detailForm.promotionalMaterialSource" class="ml20">
										<span>推广素材来源：{{ detailForm.promotionalMaterialSource }}</span>
									</div>

									<div v-if="detailForm.promotionalVidUserName" class="ml20">
										<span>视频号维护人：{{ detailForm.promotionalVidUserName }}</span>
									</div>
									<div v-if="detailForm.promotionalVid" class="ml20">
										<span class="max-w-200 fs-12">视频号名称：{{ detailForm.promotionalVid }}</span>
									</div>

									<el-select
										:class="{ 'input-border-red': detailForm.quality === '' || detailForm.quality === null }"
										class="ml20 w-180"
										:disabled="isPublicInquiry"
										v-model="detailForm.quality"
										size="small"
										placeholder="询盘质量"
									>
										<el-option v-for="key in Object.keys(qualityMap)" :key="key" :label="qualityMap[key]" :value="Number(key)">
											<div class="flex-align-center gap-5">
												<span>{{ qualityMap[key] }}</span>
												<Tooltips class="fs-12 color-999" :cont-str="qualityRemark[qualityMap[key]]" :cont-width="888" />
											</div>
										</el-option>
									</el-select>
								</div>
								<el-button v-show="!isPublicInquiry" @click="confirmSave" :type="isUpdate ? 'primary' : ''">保存信息</el-button>
							</div>
						</div>
					</transition>
				</div>

				<!-- 业务反馈表单 -->
				<div class="feedback-info" v-show="titleName != '添加'">
					<p class="detail-content-title pointer" @click="isFold.feedback = !isFold.feedback">
						<span class="mr10">业务反馈</span>
						<i :class="[isFold.feedback ? 'el-icon-arrow-right' : 'el-icon-arrow-down']"></i>
						<el-button
							v-show="this.detailForm.stage !== 5"
							type="text"
							class="p0 ml-auto el-icon-magic-stick"
							@click.stop="openRelease"
						>
							释放到公海
						</el-button>
					</p>
					<transition name="fold-transition" mode="out-in">
						<FeedbackInfo
							v-show="!isFold.feedback"
							ref="FeedbackInfo"
							:idid="detailForm.idid"
							:isPublicInquiry="isPublicInquiry"
							:implementList="implementList"
							:talktradeList="talktradeList"
							@refresh="queryDetailData(detailForm, 'refreshFeedback')"
							@openContract="openContract"
						/>
					</transition>
				</div>
				<!-- 跟单记录表单 -->
				<div class="follow-info" v-show="titleName != '添加'">
					<p class="detail-content-title pointer" @click="isFold.follow = !isFold.follow">
						<span class="mr10">跟单记录</span>
						<i :class="[isFold.follow ? 'el-icon-arrow-right' : 'el-icon-arrow-down']"></i>
						<el-checkbox
							class="ml10"
							:disabled="isFold.follow"
							v-model="isFocus"
							:true-label="1"
							:false-label="0"
							@click.stop.native="() => {}"
						>
							自动聚焦跟单输入框
						</el-checkbox>
					</p>
					<transition name="fold-transition" mode="out-in">
						<FollowInfo
							v-show="!isFold.follow"
							ref="FollowInfo"
							:idid="detailForm.idid"
							:isPublicInquiry="isPublicInquiry"
							:userList="userList"
							:documentaryRecordsList="detailForm.documentaryRecordsList || []"
							@refresh="queryDetailData(detailForm, 'refreshFollow')"
						/>
					</transition>
				</div>
				<!-- 操作日志 -->
				<div v-show="titleName != '添加'">
					<p class="detail-content-title pointer" @click="isFold.log = !isFold.log">
						<span class="mr10">操作日志</span>
						<i :class="[isFold.log ? 'el-icon-arrow-right' : 'el-icon-arrow-down']"></i>
					</p>
					<transition name="fold-transition" mode="out-in">
						<div v-show="!isFold.log" class="detail-log">
							<div class="detail-log-item" v-for="(item, index) in detailForm.basicInfoRecordVOList" :key="'oper' + index">
								<span> {{ dateFormat(item.birDateTime, 'lineM') }}</span>
								<span class="inline-block min-w-50 ml10 mr10 ellipsis"> {{ item.updateAuid }}</span>
								<div class="flex-column">
									<span v-for="(nItem, nIndex) in item.newContent" :key="nIndex" class="flex-align-center">
										<span class="mr5">将</span>
										<span class="inline-block blue ellipsis max-w-400" :title="item.oldContent[nIndex]">
											{{ item.oldContent[nIndex] }}
										</span>
										<span class="ml5 mr5">修改为</span>
										<span class="inline-block green ellipsis max-w-400" :title="nItem">{{ nItem }}</span>
									</span>
								</div>
							</div>
						</div>
					</transition>
				</div>
			</div>
		</div>

		<!-- 展期申请弹窗 -->
		<el-dialog
			:visible.sync="dialogSpread"
			width="600px"
			:close-on-click-modal="false"
			append-to-body
			@close="dialogSpread = false"
		>
			<el-row slot="title">展期申请</el-row>
			<el-form :model="spreadForm" label-width="130px" label-position="left" ref="editFormRef" :rules="spreadFormRules">
				<el-form-item label="客户工商注册名称" prop="registeredBusinessName">
					<el-col :span="24">
						<span> {{ spreadForm.registeredBusinessName }}</span>
					</el-col>
				</el-form-item>

				<el-form-item label="当前保护期" prop="protectDeadline">
					<span> {{ dateFormat(spreadForm.protectDeadline, 'lineM') }}</span>
				</el-form-item>
				<el-form-item label="展期至" prop="spreadTime">
					<el-date-picker
						v-model="spreadForm.spreadTime"
						type="date"
						class="W100"
						placeholder="请选择展期结束时间"
						value-format="timestamp"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="备注" prop="remark">
					<el-input
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
						placeholder="请输入展期备注"
						v-model="spreadForm.remark"
					></el-input>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="saveSpread">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters } from 'vuex';
import { regionData, CodeToText, TextToCode } from 'element-china-area-data';
// 来源数据
import { sourceList, industryOptions, productOptions } from '@/assets/js/inquirySource.js';
import FeedbackInfo from './FeedbackInfo'; //反馈信息
import FollowInfo from './FollowInfo/FollowInfo'; //跟单信息
import eventBus from './eventBus';
import { qualityMap, qualityRemark, frequencys } from '@/assets/js/inquirySource';

export default {
	name: 'InquiryDetail',
	components: { FeedbackInfo, FollowInfo },
	props: {
		// 询盘列表
		inquiryOptions: {
			type: Array,
			default: function () {
				return [];
			},
		},
		// 是否公海询盘
		isPublicInquiry: { type: Boolean, default: false },
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos', 'teamWorkList']), //当前登录用户信息（含团队/菜单/权限等）
		// filteredInquiryOptions() {
		// 	return this.inquiryOptions.filter(item => item.idid); // 去除空id项
		// },
		// 是超级管理
		isSuperAdmin() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.arid == 1 || item.roleName == '超级管理员') || false;
		},
		// 标签是推广管理员
		isPromoter() {
			const roleList = this.userInfos?.adminUserVO.adminUserLabelVOS || null;
			return roleList?.some(item => item.remark == '推广管理员') || false;
		},
		// 是否是咨询锁定 （超级管理员和推广管理员允许修改）
		isConsultLocked() {
			return this.detailForm.consultLocked === 1 && !this.isSuperAdmin && !this.isPromoter;
		},
		// 已备案的客户，超级管理员和业助允许修改注册名
		isEditRegisteredName() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			const isAssistant = roleList?.some(item => item.roleName == '业助'); //业务助理
			return isAssistant || this.isSuperAdmin || this.detailForm.status !== 0;
		},
		filteredInquiryOptions() {
			const uniqueOptions = _.uniqueArrayByKey(this.inquiryOptions, 'idid').filter(idid => idid);
			return uniqueOptions;
		},
		// 特殊处理字段 - 询盘日期
		idDateTime() {
			const date = this.detailForm.idDate;
			const time = this.detailForm.idTime;
			if (date && time) {
				const DATE = Number(new Date(date).getTime());
				const hour = String(time).split(':')[0] - 8;
				const min = String(time).split(':')[1];
				const TIME = (Number(hour * 60 * 60) + Number(min * 60)) * 1000;
				return DATE + TIME;
			}
			return new Date().getTime();
		},
		// 特殊处理字段 - 地区字符串
		regionString() {
			if (this.regionArray.length === 1) {
				return '其他';
			}

			if (this.regionArray.length > 1) {
				return this.regionArray.map(item => CodeToText[item]).join('-');
			}

			return '';
		},
		// 基本信息表单
		basicInfoForm() {
			return {
				channel: this.detailForm.channel,
				companyName: this.detailForm.companyName,
				industry: this.detailForm.industry || [],
				industryRemark: this.detailForm.industryRemark,
				registeredBusinessName: this.detailForm.registeredBusinessName,
				consultingCase: this.detailForm.consultingCase,
				rePurchase: this.detailForm.rePurchase,
				contactInfo: this.detailForm.contactInfo,
				customerName: this.detailForm.customerName,
				keyword: this.detailForm.keyword || [],
				idDateTime: this.detailForm.idDateTime, // 询盘日期是需要拼接转换处理的
				region: this.regionString, // 地区字段是需要拼接转换处理的
				salesman: this.detailForm.salesmanUid,
				consult: this.detailForm.consultUid,
				twid: this.detailForm.twid,
				idid: this.detailForm.idid,
				hosterTwid: this.detailForm.hosterTwid,
				ckrid: this.detailForm.ckrid,
				isProxy: this.detailForm.isProxy,
				marketingDivision: this.detailForm.marketingDivision, //营销分管
				businessPartnerUnitUid: this.detailForm.businessPartnerUnitUid, //BPUL

				quality: this.detailForm.quality, //聚心城需求：添加询盘时确定质量
			};
		},
		// 是否修改
		isUpdate() {
			const FLAG = JSON.stringify(this.basicInfoForm) !== JSON.stringify(this.basicInfoFormCopy);
			// console.log('basicInfoForm_isUpdate', FLAG);
			return FLAG;
		},
		// 展示上下页切换
		showPage() {
			return this.titleName !== '添加' && this.filteredInquiryOptions.length > 1 && this.nowIndex >= 0;
		},
		// 显示托管方
		showHoster() {
			return this.hosterList.length >= 1;
		},
		//业务顾问列表(标签：业务)
		salesmanList() {
			return this.userList?.filter(user => user?.userLabel?.includes('业务')) || [];
		},
		//实施顾问列表(标签：实施)
		implementList() {
			return this.userList?.filter(user => user?.userLabel?.includes('实施')) || [];
		},
		//咨询人员列表(标签：咨询)
		consultList() {
			return this.userList?.filter(user => user?.userLabel?.includes('咨询')) || [];
		},
		//营销分管人员列表(标签：营销分管)
		marketingList() {
			return this.userList?.filter(user => user?.userLabel?.includes('营销')) || [];
		},
		//BPUL人员列表(标签：BPUL)
		BPUList() {
			return this.userList?.filter(user => user?.userLabel?.includes('BPUL')) || [];
		},

		//谈单人员列表(标签：谈单)
		talktradeList() {
			return this.userList?.filter(user => user?.userLabel?.includes('谈单')) || [];
		},
	},

	data() {
		return {
			titleName: '', //标题类型：新增/修改
			showTips: false, //提示显隐
			showCom: false, //控制弹窗显隐
			isFocus: true, //控制自动聚焦
			nowIndex: -1,
			//控制折叠显隐
			isFoldAll: false,
			isFold: {
				basic: false, //基本信息
				consult: false, //咨询
				feedback: false, //反馈
				follow: false, //跟单
				log: false, //日志
			},

			userList: [], //渠道/代理人员列表
			hosterList: [], //托管方列表

			sourceList, // 来源列表
			industryOptions, //行业选项
			productOptions, //产品选项
			regionData, // 地区列表
			regionArray: [], // 地区代码
			// 明细表单
			detailForm: {
				area: '',
				basicInfoRecordVOList: [],
				businessPartnerUnitUid: '', //BPUL
				callRecording: '',
				channel: '',
				city: '',
				ckrid: '',
				clientNo: '',
				companyName: '',
				consultName: '',
				consultUid: '',
				consultingCase: '',
				contactInfo: '',
				contractName: '',
				contractUrl: '',
				count: '',
				createName: '',
				createTime: '',
				customerName: '',
				documentaryRecordsList: [],
				estimatedAmount: '',
				expectedMonth: '',
				feedback: '',
				hosterName: '',
				hosterTwid: '',
				idDate: '',
				idTime: '',
				idid: '',
				industry: [],
				industryProficiency: '', //行业熟练度
				industryRemark: '', //行业备注
				implementName: '',
				implementUid: '',
				keyword: [], //产品（原关键词）
				lastDate: '',
				losingOrderReasons: '', //丢单原因
				number: '',
				province: '',
				quality: '',
				rePurchase: '',
				registeredBusinessName: '',
				restDays: '',
				salesmanName: '',
				salesmanUid: '',
				signingDate: '',
				stage: '',
				status: '',
				talktradeName: '',
				talktradeUid: '',
				twid: '',
				twidName: '',

				consultingDate: '',
				followUpFrequency: '',
				followUpStrategy: '',
				product: '',
				mainProcess: '',
				marketingDivision: '', //营销分管
				scale: '',
				projectPhase: '',
				contactBackground: '',
				consultingOther: '',

				idDateTime: '', //询盘日期时间戳
				isUpLoadAudio: false, //上传录音状态
				isUpLoadContract: false, //上传合同状态
			},

			dialogSpread: false, // 展期弹窗
			// 展期表单
			spreadForm: {
				status: '', //有效状态
				spreadStatus: '', //展期状态
				surplusValidDay: '',

				ckrid: '',
				remark: '',
				spreadTime: '',
			},
			spreadFormRules: {
				spreadTime: [{ required: true, message: '请输入展期日期', trigger: 'change' }],
			},
			detailFormCopy: {}, //克隆数据用于判断
			basicInfoFormCopy: {}, //克隆数据用于判断
			formRules: {
				idDateTime: [{ required: true, message: '请选择日期时间', trigger: 'change' }],
				customerName: [{ required: true, message: '客户称呼', trigger: 'blur' }],
				channel: [{ required: true, message: '请选择来源', trigger: 'change' }],
				// keyword: [{ required: true, message: '请选择产品', trigger: 'change' }],
				contactInfo: [{ required: true, message: '请输入联系方式', trigger: 'blur' }],
				region: [{ required: true, message: '请选择客户所在地区！', trigger: 'blur' }],
				consultingCase: [{ required: true, message: '请输入咨询情况！', trigger: 'blur' }],
				twid: [{ required: true, message: '请输入分销/代理信息', trigger: 'change' }],
				quality: [{ required: true, message: '请选择询盘质量', trigger: 'change' }],
				// salesmanUid: [{ required: true, message: '请输入业务顾问信息！', trigger: 'change' }],
				// consultUid: [{ required: true, message: '请输入咨询人员信息！', trigger: 'change' }],
			},

			qualityMap, // 询盘质量
			qualityRemark, // 询盘质量说明
			frequencys, // 跟进频次
		};
	},
	mounted() {
		// 新增地区：其他
		const lastRegion = this.regionData[this.regionData.length - 1];
		if (lastRegion.label !== '其他') {
			this.regionData.push({
				label: '其他',
				value: '0',
			});
		}
		eventBus.$emit(`updateDetailForm_${this.$parent.$options.name}`, this.detailForm); //更新事件总线的 detailForm 对象
	},
	beforeDestroy() {
		eventBus.$off(`updateDetailForm_${this.$parent.$options.name}`);
	},
	destroyed() {
		this.detailForm = null;
		this.detailFormCopy = null;
		// this.inquiryOptions = null;
	},
	watch: {
		isFoldAll(newVal) {
			if (newVal) {
				// 全部收起
				Object.keys(this.isFold).forEach(key => {
					this.isFold[key] = true;
				});
			} else {
				// 全部展开
				Object.keys(this.isFold).forEach(key => {
					this.isFold[key] = false;
				});
			}
		},
	},
	methods: {
		// 打开爱企查
		openUrl(name) {
			const baseUrl = 'https://aiqicha.baidu.com/s?q=';
			const url = `${baseUrl}${encodeURIComponent(name)}&t=0`;
			window.open(url, '_blank');
		},
		// 复制文本
		copyToClipboard: _.copyToClipboard,
		// 打开合同
		openContract() {
			this.$emit('openContract', this.detailForm);
		},
		// 保存展期
		saveSpread() {
			if (!this.spreadForm.spreadTime) {
				this.$message.warning('请选择需延长到的展期日期！');
				return;
			}

			const API = 'applyLengthenSpread';
			this.$axios[API](
				JSON.stringify({
					ckrid: this.spreadForm.ckrid,
					remark: this.spreadForm.remark,
					spreadTime: this.spreadForm.spreadTime,
				}),
			)
				.then(res => {
					if (res.data.success) {
						this.$succ('保存成功!');
						this.dialogSpread = false;
						this.queryDetailData(this.detailForm, 'spread');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		},
		// 释放到公海
		openRelease: _.debounce(async function () {
			this.$confirm('确认放入公海?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(async () => {
					const API = 'useHighSeas';
					try {
						const res = await this.$axios[API](JSON.stringify({ idid: this.detailForm.idid }));
						if (res.data.success) {
							this.queryDetailData(this.detailForm, 'release');
							this.$succ(res.data.message);
						} else {
							this.$err(res.data.message);
						}
					} catch (error) {
						console.error(`${API} |` + error);
					}
				})
				.catch(() => {
					this.$message.info('已取消！');
				});
		}),
		// 复购询盘
		saveRePurchase: _.debounce(async function () {
			const API = 'inquiryRepurchase';
			try {
				const res = await this.$axios[API](JSON.stringify({ idid: this.detailForm.idid }));
				if (res.data.success) {
					await this.$confirm('复购询盘已生成, 是否跳转至新生成的复购询盘的详情页?', '提示', {
						confirmButtonText: '进入复购询盘',
						cancelButtonText: '停留在原询盘',
					})
						.then(() => {
							this.$emit('close'); // 用于获取新的inquiryOptions，非关闭询盘详情
							this.detailForm.idid = res.data.data;
							this.queryDetailData(_.deepClone(this.detailForm), 'copy'); // 跳转到询盘详情
							this.$message.success('操作成功，当前为新生成的复购询盘详情页');
						})
						.catch(() => {
							this.$message.info('已取消，停留在原询盘详情页');
						});
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.log(`${API} |` + error);
			}
		}),
		// 确认保存(若业务顾问/咨询人员未填写时，弹窗提示将自动分配)
		confirmSave: _.debounce(async function () {
			if (!this.basicInfoForm.salesman || !this.basicInfoForm.consult) {
				this.$confirm(
					`若业务顾问/咨询人员未填写时，保存后系统将会把该询盘自动分配给其他业务顾问/咨询人员，请悉知！`,
					'重要提醒',
					{
						confirmButtonText: '确定',
						cancelButtonText: '取消',
					},
				)
					.then(() => {
						this.saveEdit('save');
					})
					.catch(() => {
						this.$message.info('取消返回！');
						return;
					});
			} else {
				this.saveEdit('save');
			}
		}),
		// 保存基本信息(修改询盘基本信息/创建一个新的询盘)
		saveEdit: _.debounce(async function (type) {
			// 所属行业为其他行业时必须输入行业备注
			if (this.detailForm.industry?.includes('其他行业') && !this.detailForm.industryRemark) {
				this.$message.warning('请填写行业备注');
				return;
			}
			// this.detailForm.region = this.regionString;
			if (type !== 'public' && _.checkRequired(this.detailForm, this.formRules)) {
				return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			}
			let API = this.detailForm.idid ? 'basicInfoUpdate' : 'addInquiryDocumentary';

			// 公海询盘领取时将业务顾问修改为当前用户并调用专用接口
			if (this.detailForm.idid && type == 'public' && this.isPublicInquiry) {
				const nowUserID = this.userInfos.adminUserVO.auid;
				// 如果没有录音 或者 原咨询人等于当前用户 则咨询人设为当前用户
				const sameUser = this.detailForm.oldConsultantConsult == nowUserID;
				const noCallRecording = !this.detailForm.callRecording;
				if (sameUser || noCallRecording) {
					this.detailForm.consultUid = nowUserID;
					console.log(`${sameUser ? '原咨询人为当前用户！' : ''} ${noCallRecording ? '没有上传录音！' : ''}`);
				}
				this.detailForm.salesmanUid = nowUserID;
				API = 'basicInfoUpdateForSharedSeaItem'; //询盘基本信息修改, 公海询盘领取专用
			}

			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.basicInfoForm }));
				if (res.data.success) {
					if (!this.detailForm.idid || type == 'public') {
						// 新建或领取成功关闭窗口
						this.showCom = false;
						this.showTips = false;
						this.detailForm = _.resetValues(this.detailForm); //重置对象
						this.$emit('close');
						type == 'public' && this.$message.success('领取成功，该询盘已归属于你！');
						return;
					}
					if (type == 'record') {
						await this.saveRecord(); //需要保存基本信息后再备案
						return;
					}
					this.queryDetailData(this.detailForm, 'refreshBasicInfo');
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.log(`${API} |` + error);
			}
		}),
		// 客户备案(根据后台要求，需要保存基本信息后再备案)
		saveRecord() {
			if (!this.detailForm.registeredBusinessName) {
				return this.$message.warning('工商注册名为空！');
			}
			const recordForm = {
				abbreviation: this.detailForm.companyName,
				clientNeed: this.detailForm.consultingCase,
				linkman: this.detailForm.customerName,
				linkphone: this.detailForm.contactInfo,
				region: this.regionString, // 地区字段是需要拼接转换处理的
				registeredBusinessName: this.detailForm.registeredBusinessName,
				salesman: this.detailForm.salesmanUid,
				channel: this.detailForm.channel,
				idid: this.detailForm.idid,
				twid: this.detailForm.twid,
			};
			this.$axios
				.addKeepRecordClient(JSON.stringify({ ...recordForm }))
				.then(res => {
					if (res.data.success) {
						this.$succ('备案成功！');
						this.queryDetailData(this.detailForm, 'refreshRecord'); //备案成功后刷新数据
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('addKeepRecordClient |' + error);
				});
		},
		// 查询询盘详情(type: change切换上下页或选中选项 refresh刷新 copy复购 init初次点击进入时 record备案 release释放公海)
		queryDetailData: _.debounce(async function ({ idid }, type, api = 'selectInquiryDocumentaryOne') {
			if (!idid) return;
			try {
				const res = await this.$axios[api](JSON.stringify({ idid }));
				if (res.data.success) {
					const { twid, salesmanUid } = res.data.data;
					await this.querysSalesmanList(twid); //根据代理查询业务顾问列表
					await this.changeSalesman(salesmanUid, 'query'); //修改业务时根据业务顾问查询托管方列表
					res.data.data.idTime = res.data.data?.idTime?.replace('-', ':') || ''; //时间格式转换
					// 如果没有填写商机质量默认为询盘质量
					res.data.data.businessOpportunityQuality =
						res.data.data.businessOpportunityQuality == null ? res.data.data.quality : res.data.data.businessOpportunityQuality;
					// 如果没有填写咨询时间默认为当前时刻
					res.data.data.consultingDate = !res.data.data.consultingDate ? new Date().getTime() : res.data.data.consultingDate;
					// 获取备案信息，如果没有则清空
					if (res.data.data?.keepRecordClientVO) {
						this.spreadForm = { ...this.spreadForm, ...res.data.data.keepRecordClientVO };
					} else {
						this.spreadForm = _.resetValues(this.spreadForm);
					}
					if (['refreshBasicInfo', 'refreshConsult', 'refreshFeedback', 'refreshFollow', 'refreshRecord'].includes(type)) {
						// 保存后的刷新对应的表单或其他信息 （暂无好的想法优化，字段太多按需添加）
						const refreshFuncs = {
							refreshBasicInfo: () => {
								console.log('刷新基本信息和操作日志！');
								return [
									'area',
									'city',
									'province',
									'basicInfoRecordVOList',
									'salesmanUid',
									'consultUid',
									'businessPartnerUnitUid',
								];
							},
							refreshConsult: () => {
								console.log('刷新咨询信息、咨询完成时间等！');
								return ['consultationTime', 'documentaryRecordsList'];
							},
							refreshFeedback: () => {
								console.log('刷新业务反馈、业务接手时间等！');
								return ['businessTakeoverTime'];
							},
							refreshFollow: () => {
								console.log('刷新跟单记录！');
								return ['documentaryRecordsList'];
							},
							refreshRecord: () => {
								console.log('刷新备案信息！');
								return ['status', 'clientNo', 'registeredBusinessName']; //备案成功后刷新数据，只需要更新备案状态备案号等信息
							},
						};
						const refreshKeys = refreshFuncs[type]();
						refreshKeys.forEach(key => {
							this.$set(this.detailForm, key, res.data.data[key]);
							this.$set(this.detailFormCopy, key, res.data.data[key]);
						});
					} else {
						this.detailForm = { ...this.detailForm, ...res.data.data };
						eventBus.$emit(`updateDetailForm_${this.$parent.$options.name}`, this.detailForm); //更新事件总线的 detailForm 对象
						console.log('eventBus - emit ', `updateDetailForm_${this.$parent.$options.name}`);

						this.$set(this.detailForm, 'idDateTime', this.idDateTime); //日期格式转换
						this.regionArray = this.getRegionArray(); //地区信息三级联动数组获取（由表单的几个数据拼成，只是用于显示）
					}
					this.$nextTick(() => {
						if (this.filteredInquiryOptions?.length > 1) {
							this.nowIndex = this.filteredInquiryOptions?.findIndex(item => item?.idid == this.detailForm.idid); //当前选中项下标
						}
						this.basicInfoFormCopy = _.deepClone(this.basicInfoForm); //克隆数据
						this.detailFormCopy = _.deepClone(this.detailForm); //克隆数据（用于判断数据是否修改）
					});
					this.$store.commit('setInquiryList', _.deepClone(this.detailForm)); // Vuex 存储到本地
					// 当前组件页面切换查询询盘
					if (type == 'change') {
						this.$refs?.FollowInfo?.clearFollowForm(); //清空跟单输入框的信息
					}

					// 刚进入组件页面初始状态
					if (type == 'init') {
						this.showCom = true;
						setTimeout(() => {
							this.isFocus = JSON.parse(localStorage.getItem('InquiryDetail_isFocus')) || false; // 获取本地存储的聚焦状态
							this.isFold.follow == false && this.isFocus && this.$refs?.FollowInfo?.focus(); //如果没有折叠且聚焦状态打开则定位到输入框(需要等窗口完全滑动完)
						}, 888);
					}

					// 如果有跟单记录 -> 查询出差申请记录
					// if (this.detailForm.documentaryRecordsList?.length > 0) {
					// 	this.$refs?.FollowInfo?.queryTravelData();
					// }
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.log(`${api} |` + error);
			}
		}),
		// 修改业务时查询业务顾问对应的托管方列表
		async changeSalesman(auid, type) {
			if (!auid) {
				this.detailForm.hosterTwid = '';
				this.hosterList = [];
				return;
			}
			if (type == 'change') {
				// 若用户为无效:咨询情况，询盘质量，跟进策略，跟进频次均为无效 ；阶段：咨询
				const salesmanName = this.salesmanList.find(item => item.auid == auid)?.userName;
				if (salesmanName?.includes('无效')) {
					this.detailForm.consultingCase = '无效'; //咨询情况
					await this.saveEdit();
					await this.$refs.ConsultInfo?.setInvalid(); // 设置无效值
				}
			}
			try {
				const res = await this.$axios.selectHoster(JSON.stringify({ auid }));
				if (res.data.success) {
					this.hosterList = res.data.data.twidAndNameList || [];
					// 如果hosterTwid 不在该列表中，则清空
					if (!this.hosterList.some(item => item.twid == this.detailForm.hosterTwid)) {
						this.detailForm.hosterTwid = '';
					}
				} else {
					this.hosterList = [];
					this.detailForm.hosterTwid = '';
				}
			} catch (error) {
				console.log('selectHoster |' + error);
			}
		},
		// 显示滑窗组件
		showDetailCom(type, row, api) {
			this.titleName = type;
			if (type == '修改') {
				this.queryDetailData(_.deepClone(row), 'init', api);
			} else {
				if (this.detailForm.idid) {
					this.detailForm = _.resetValues(this.detailForm);
				}
				// 添加
				this.isFold.basic = false;
				this.detailForm.idDateTime = new Date().getTime();
				this.regionArray = []; //地区信息三级联动数组
				this.detailFormCopy = _.deepClone(this.detailForm);
				this.showCom = true;
			}
		},

		//点击返回关闭滑窗
		closeDetailCom() {
			// 本地存储聚焦状态
			localStorage.setItem('InquiryDetail_isFocus', this.isFocus);
			let isSameData = true;
			isSameData = JSON.stringify(this.detailForm) == JSON.stringify(this.detailFormCopy);
			console.log(this.detailForm, this.detailFormCopy);
			console.log('isSameData', isSameData);
			if (!isSameData) {
				this.$confirm(`当前页面有内容修改但未保存，是否继续退出（不保存）操作？`, '重要提醒', {
					confirmButtonText: '确定退出',
					cancelButtonText: '取消退出',
				})
					.then(() => {
						this.clearDetailData(); // 关闭并清空数据
					})
					.catch(() => {
						this.$message.info('取消返回！');
						return;
					});
			} else {
				this.clearDetailData(); // 关闭并清空数据
			}
		},
		// 关闭并清空数据
		clearDetailData() {
			this.$emit('close');
			this.showCom = false;
			this.showTips = false;
			this.nowIndex = -1;
			this.detailForm = _.resetValues(this.detailForm); //重置对象
			this.spreadForm = _.resetValues(this.spreadForm); //重置对象
			eventBus.$emit(`updateDetailForm_${this.$parent.$options.name}`, this.detailForm); //更新事件总线的 detailForm 对象
			this.$refs?.FollowInfo?.clearFollowForm();
		},
		// 检查公司工商注册名是否存在
		checkRegistered: _.debounce(function ({ registeredBusinessName }) {
			// 注册名存在空格时显示提示
			this.showTips = registeredBusinessName && /\s/.test(registeredBusinessName) ? true : false;
			this.$axios
				.checkName(JSON.stringify({ registeredBusinessName }))
				.then(res => {
					if (res.data.success) {
						// 未存在该公司工商注册名
					} else {
						if (registeredBusinessName) {
							this.$confirm('注意,该公司工商注册名已存在, 是否继续操作?', '提示', {
								confirmButtonText: '确定',
								cancelButtonText: '取消',
								type: 'warning',
								dangerouslyUseHTMLString: true,
								message: `
									<p>注意,该公司工商注册名"${registeredBusinessName}"已存在, 是否继续操作?</p>
									<strong class="red fs-16">${res.data.message}</strong>
								`,
							})
								.then(() => {
									this.$message.success('您已悉知，请继续操作!');
								})
								.catch(() => {
									// this.detailForm.registeredBusinessName = '';
									this.$message.info('您已取消，请重新输入新的工商注册名！');
								});
						}
					}
				})
				.catch(error => {
					console.log('checkName |' + error);
				});
		}, 200),
		// 检查联系方式是否存在
		checkContactInfo: _.debounce(function ({ contactInfo }) {
			this.$axios
				.checkPhone(JSON.stringify({ contactInfo }))
				.then(res => {
					if (res.data.success) {
						// 未存在该联系方式
					} else {
						if (contactInfo) {
							this.$confirm('注意,该联系方式已存在, 是否继续操作?', '提示', {
								confirmButtonText: '确定',
								cancelButtonText: '取消',
								type: 'warning',
								dangerouslyUseHTMLString: true,
								message: `
									<p>注意,该联系方式"${contactInfo}"已存在, 是否继续操作?</p>
									<strong class="red fs-16">归属：${res.data.message}</strong>
								`,
							})
								.then(() => {
									this.$message.success('您已悉知，请继续操作!');
								})
								.catch(() => {
									// this.detailForm.contactInfo = '';
									this.$message.info('您已取消，请重新输入新联系方式！');
								});
						}
					}
				})
				.catch(error => {
					console.log('checkPhone |' + error);
				});
		}),

		// 查询分销/代理下的业务顾问列表
		async querysSalesmanList(twid) {
			if (!twid) {
				// 清除分销/代理时  相关人员则清空
				this.detailForm.consultUid = '';
				this.detailForm.salesmanUid = '';
				this.detailForm.marketingDivision = '';
				this.detailForm.businessPartnerUnitUid = '';
				this.detailForm.consultUid = '';
				// this.detailForm.talktradeUid = '';
			}

			try {
				const res = await this.$axios.selectSalesmanByTwid(JSON.stringify({ twid, counselor: '' }));
				if (res.data.success) {
					this.userList = res.data.data || [];
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.log('selectSalesmanByTwid |' + error);
			}
		},

		// 获取三级联动的数组
		getRegionArray() {
			const { province, city, area } = this.detailForm;
			if (province == '其他') {
				this.detailForm.city = '';
				this.detailForm.area = '';
				this.detailFormCopy = _.deepClone(this.detailForm);
				return ['0'];
			} else if (province && city && area) {
				try {
					const provinceCode = TextToCode[province]?.code || '';
					const cityCode = TextToCode[province][city]?.code || '';
					const areaCode = TextToCode[province][city][area]?.code || '';
					return [provinceCode, cityCode, areaCode];
				} catch (error) {
					console.log({ error });
					return [];
				}
			} else {
				return [];
			}
		},

		dateFormat: _.dateFormat, //日期format
		jointString: _.jointString, // 拼接字符串
	},
};
</script>
<style lang="scss" scoped></style>
