<template>
	<!-- 该组件用于以弹窗形式预览展示不同格式的文件 -->
	<div class="FilePopover">
		<el-popover :placement="placement" v-model="isShow" :trigger="trigger" @show="isShow = true" @hide="isShow = false">
			<!-- 显示 -->
			<template #reference>
				<!-- 仅显示图标 -->
				<span v-if="isIcon" :class="icon"></span>
				<!-- 方框盒子显示 -->
				<div v-else-if="isBox" :class="`boxClass ${boxClass}`">
					<!-- 图片 -->
					<el-image v-if="isImage" lazy fit="scale-down" :src="url" :preview-src-list="trigger == 'click' ? [] : [url]">
					</el-image>
					<!-- 非图片 -->
					<div class="file-box" v-else>
						<i class="fs-24" :class="getFileIcon(url)"></i>
						<Tooltips class="blue W100 pr10 pl10" :cont-str="content" :cont-width="100" />
					</div>
				</div>
				<!-- 默认显示 -->
				<Tooltips v-else class="blue pointer" :cont-str="content || url" :cont-width="100" />
			</template>

			<!-- 冒泡显示 -->
			<div>
				<div v-if="isImage">
					<el-image
						fit="scale-down"
						:style="{ 'max-width': imageWidth + 'px', 'max-height': imageHeight + 'px' }"
						:src="url"
						:preview-src-list="[url]"
					></el-image>
				</div>
				<div v-else-if="isOffice">
					<iframe :src="officeUrl" :width="iframeWidth" :height="iframeHeight" frameborder="0"></iframe>
				</div>
				<div v-else-if="isPdf">
					<iframe :src="url" :width="iframeWidth" :height="iframeHeight" frameborder="0"></iframe>
				</div>
				<div v-else class="red fs-12 m-auto">
					<p>当前不支持预览该格式“{{ fileExtension }}”的文件！</p>
				</div>
			</div>
		</el-popover>
	</div>
</template>

<script>
export default {
	name: 'FilePopover',
	props: {
		isIcon: Boolean, //只显示图标
		icon: { type: String, default: 'el-icon-paperclip' },
		isBox: Boolean, //方框盒子显示
		boxClass: {
			type: String,
			default: '',
		},
		url: String, // 需要预览的文件路径
		content: {
			// 需要显示的文本默认为文件路径
			type: String,
			default: '',
		},
		// 预览文件宽高
		iframeWidth: {
			type: [String, Number],
			default: 888,
		},
		iframeHeight: {
			type: [String, Number],
			default: 888,
		},
		// 预览图片宽高
		imageWidth: {
			type: [String, Number],
			default: 400,
		},
		imageHeight: {
			type: [String, Number],
			default: 400,
		},
		// 触发方式
		trigger: {
			type: String,
			default: 'hover', //click hover
		},
		// 显示位置
		placement: {
			type: String,
			default: 'left',
		},
	},
	data() {
		return {
			isShow: false,
		};
	},
	computed: {
		// 获取文件扩展名称
		fileExtension() {
			if (!this.url) return '';
			return this.url.substring(this.url.lastIndexOf('.')).toLowerCase();
		},
		// 判断文档类型
		fileType() {
			const fileTypeMapping = {
				'.jpeg': 'image',
				'.gif': 'image',
				'.jpg': 'image',
				'.png': 'image',
				'.bmp': 'image',
				'.pic': 'image',
				'.svg': 'image',
				'.docx': 'office',
				'.doc': 'office',
				'.xls': 'office',
				'.xlsx': 'office',
				'.ppt': 'office',
				'.pptx': 'office',
				'.pdf': 'pdf',
			};
			return fileTypeMapping[this.fileExtension] || '';
		},
		isImage() {
			return this.fileType === 'image';
		},
		isOffice() {
			return this.fileType === 'office' && this.isShow;
		},
		isPdf() {
			return this.fileType === 'pdf' && this.isShow;
		},
		officeUrl() {
			return this.isOffice ? `https://view.officeapps.live.com/op/view.aspx?src=${this.url}` : '';
		},
	},
	watch: {},
	methods: {
		// 判断文档类型
		getFileIcon(file) {
			const fileTypeMapping = {
				'.jpeg': 'JPG',
				'.gif': 'GIF',
				'.jpg': 'JPG',
				'.png': 'PNG',
				'.bmp': 'tupianziliao',
				'.pic': 'tupianziliao',
				'.svg': 'tupianziliao',
				'.docx': 'WORD',
				'.doc': 'WORD',
				'.xls': 'ECEL',
				'.xlsx': 'ECEL',
				'.ppt': 'PPT',
				'.pptx': 'PPT',
				'.pdf': 'PDF',
			};
			const fileExtension = file?.substring(file?.lastIndexOf('.')).toLowerCase();
			return `office-icon-${fileTypeMapping[fileExtension] || 'zonghewendang'}`;
		},
	},
};
</script>
<style lang="scss" scoped>
.FilePopover {
	// 盒子样式
	.boxClass {
		padding: 10px;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100%;
		border: 1px solid #ccc;
		border-radius: 3%;
		cursor: pointer;
		overflow: hidden;
		// 文档盒子
		.file-box {
			width: inherit;
			height: inherit;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
		}
	}
}
</style>
