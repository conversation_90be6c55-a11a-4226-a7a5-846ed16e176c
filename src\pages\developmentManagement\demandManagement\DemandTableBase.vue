<template>
	<!-- 需求信息(新增/编辑) -->
	<div class="DemandTableBase">
		<div class="header-info pb10 fs-12">
			<div class="flex-justify-between gap-10">
				<div class="flex-align-center gap-10">
					<div class="label-required min-w-40">客户</div>
					<el-autocomplete
						ref="registeredBusinessName"
						v-model="nowDetailForm.registeredBusinessName"
						placeholder="请输入客户名称（询盘）"
						clearable
						size="mini"
						:debounce="200"
						:fetch-suggestions="(queryStr, cb) => queryByInput(queryStr, cb, '客户', nowDetailForm)"
						@select="handleSelect($event, '客户', nowDetailForm)"
						@clear="$refs.registeredBusinessName.activated = true"
					>
						<template slot-scope="{ item }">
							<div class="flex-align-center gap-10">
								<Tooltips
									class="max-w-200 inline-block"
									:cont-str="`${item.registeredBusinessName || item.companyName || '无客户名称'} `"
									:cont-width="220"
								/>
								<span class="ml-auto">
									<span>【{{ item.rePurchase ? '复购' : '新客' }} / {{ item.status ? '未备案' : '已备案' }}】</span>
								</span>
							</div>
						</template>
					</el-autocomplete>
				</div>
				<div class="flex-align-center gap-10">
					<div class="min-w-25">合同</div>
					<el-autocomplete
						ref="contractNo"
						v-model="nowDetailForm.contractNo"
						placeholder="请输入合同编号"
						clearable
						size="mini"
						:debounce="200"
						:fetch-suggestions="(queryStr, cb) => queryByInput(queryStr, cb, '合同', nowDetailForm)"
						@select="handleSelect($event, '合同', nowDetailForm)"
						@clear="$refs.contractNo.activated = true"
					>
						<template slot-scope="{ item }">
							<div class="flex-align-center gap-10">
								<Tooltips
									class="max-w-300 inline-block"
									:cont-str="`${item.contractNo || '无合同编号'} | ${item.contractName || '无合同名称'} `"
									:cont-width="280"
								/>
								<span class="ml-auto" v-show="projectTypeMap[item.projectType]">【{{ projectTypeMap[item.projectType] }}】</span>
							</div>
						</template>
					</el-autocomplete>
					<el-button
						v-show="nowDetailForm.dmid"
						type="text"
						class="el-icon-document m0 p0"
						@click="$emit('openContract', nowDetailForm)"
					></el-button>
				</div>
				<div class="flex-align-center gap-10">
					<div class="label-required min-w-60">项目类别</div>
					<el-select size="mini" v-model="nowDetailForm.projectCategory" placeholder="请选择项目类别" clearable>
						<el-option
							v-for="key in Object.keys(projectCategoryMap)"
							:key="key"
							:label="projectCategoryMap[key]"
							:value="Number(key)"
						>
						</el-option>
					</el-select>
				</div>
			</div>
		</div>
		<!-- 表格 -->
		<u-table
			class="table-main detail-table W100 input-border-none"
			size="mini"
			ref="uTableRef"
			max-height="400px"
			:row-height="30"
			:data="nowTableData"
			:row-style="{ height: '0' }"
			:cell-style="{ padding: '0 0', borderBottom: '1px solid #e9e9e9' }"
			:header-cell-style="{ border: 'transparent', padding: '5px 0 !important' }"
			:show-summary="showSummary"
			:summary-method="summaryMethod"
		>
			<u-table-column
				v-for="col in tableColumn"
				:key="col.colNo"
				:label="col.colName"
				:prop="col.colNo"
				:align="col.align"
				:width="col.width"
			>
				<template slot-scope="scope">
					<!-- 客户要求完成日期 -->
					<el-date-picker
						v-if="col.colNo == 'customerRequiredCompletionDate'"
						v-model="scope.row[col.colNo]"
						type="date"
						size="mini"
						placeholder="要求完成日期"
						class="min-w-150"
						value-format="timestamp"
					>
					</el-date-picker>

					<!-- 需求类别 -->
					<el-select
						v-else-if="col.colNo == 'demandType'"
						v-model="scope.row[col.colNo]"
						size="mini"
						placeholder="需求类别"
						clearable
						filterable
					>
						<el-option v-for="key in Object.keys(demandTypeMap)" :key="key" :label="demandTypeMap[key]" :value="Number(key)">
						</el-option>
					</el-select>
					<!-- 需求描述 -->
					<el-input v-else size="mini" v-model="scope.row[col.colNo]" :placeholder="col.colName" clearable></el-input>
				</template>
			</u-table-column>
			<u-table-column label="" width="100" align="center">
				<template slot-scope="scope">
					<el-button type="text" class="el-icon-plus" @click="addRow(scope.row, scope.$index)"></el-button>
					<el-button type="text" class="el-icon-copy-document" @click="copyRow(scope.row, scope.$index)"></el-button>
					<el-button type="text" class="el-icon-delete" @click="delRow(scope.row, scope.$index)"></el-button>
				</template>
			</u-table-column>
		</u-table>
		<!-- 日志 -->
		<div v-if="nowLogList.length > 0" class="mt10 max-h-300 overflow-y-auto">
			<ExpandableList :items="nowLogList" :defaultCount="1">
				<template slot-scope="{ item }">
					<div class="flex-align-center gap-10 fs-12" :class="item.submitReject ? '' : 'red'">
						<div>{{ item.submitRejectName }}</div>
						<div>{{ dateFormat(item.submitRejectTime, 'lineM') }}</div>
						<div>{{ item.submitReject ? '提交' : '驳回' }}</div>
						<div>{{ item.remarks || '' }}</div>
					</div>
				</template>
			</ExpandableList>
		</div>
		<!-- 操作 -->
		<div class="operation-btn pb10 pt10">
			<el-button type="text" size="small" class="el-icon-plus" @click="addRow(null, null)"> 添加一行</el-button>
			<div class="flex-justify-between gap-10">
				<el-button :disabled="!nowTableData.length" class="w-100" :type="isUpdate ? 'primary' : ''" size="small" @click="save">
					保存</el-button
				>
				<el-button v-if="nowDetailForm.dsid" class="w-100" :type="isUpdate ? '' : 'primary'" size="small" @click="submit">
					提交
				</el-button>
			</div>
		</div>
	</div>
</template>
<script>
import { dateFormat, resetValues, jointString, deepClone } from '@/util/tool';
import { bigAdd, bigDiv } from '@/util/math';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import { projectTypeMap } from '@/assets/js/contractSource';
import { demandTypeMap, projectCategoryMap } from '@/assets/js/projectSource';

import ExpandableList from '@/components/ExpandableList.vue';
export default {
	name: 'DemandTableBase',
	components: { ExpandableList },
	props: {
		// 索引
		componentIndex: {
			type: Number,
			default: 0,
		},
		// 详情表单
		detailForm: {
			type: Object,
			default: () => {},
		},

		// 表格数据
		tableData: {
			type: Array,
			default: () => [],
		},

		// 表格映射
		tableInfo: {
			type: Object,
			default: () => {},
		},
		// 用户列表
		userList: {
			type: Array,
			default: () => [],
		},
		// 是否显示组件
		showCom: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			demandTypeMap, // 需求类别
			projectTypeMap, // 项目类别
			projectCategoryMap, // 需求项目类别
			// 需求文档项目类别
			nowTableData: [], // 当前表格数据
			nowTableDataCopy: [], // 当前表格数据副本
			nowDetailForm: {
				status: 1,
			}, // 当前详情表单
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']),
		// 表格标题
		titleName() {
			return this.tableInfo?.title || '';
		},
		// 表格列
		tableColumn() {
			return this.tableInfo?.tableColumn || [];
		},
		// 汇总列
		summaryKeys() {
			return this.tableInfo?.summaryKeys || [];
		},
		// 当前日志(最近的日期排在前面)
		nowLogList() {
			return (
				this.detailForm?.demandSupervisionLogsVOS
					?.filter(item => item.status == this.componentIndex)
					.sort((a, b) => b.submitRejectTime - a.submitRejectTime) || []
			);
		},
		// 是否显示表尾合计
		showSummary() {
			return this.summaryKeys.length > 0;
		},
		// 是否提示保存按钮
		isUpdate() {
			return JSON.stringify(this.nowTableData) != JSON.stringify(this.nowTableDataCopy);
		},
	},
	// 监控data中的数据变化
	watch: {
		'detailForm.dsid': {
			handler(newVal) {
				this.nowDetailForm = { ...this.nowDetailForm, ...this.detailForm };
				this.nowDetailForm.status = this.detailForm.status ? this.detailForm.status : 1;
				this.nowTableData = this.tableData;
				this.nowTableDataCopy = deepClone(this.nowTableData);
			},
			immediate: true,
		},
		// 是否显示组件
		showCom(newVal) {
			if (!newVal) {
				this.clear();
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.nowDetailForm = this.detailForm;
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {
		this.clear();
	},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 表尾合计
		summaryMethod({ columns, data }) {
			const means = ['合计']; // 合计
			for (let columnIndex = 1; columnIndex < columns.length; columnIndex++) {
				const column = columns[columnIndex];
				const values = data.map(item => {
					if (this.summaryKeys.includes(column.property)) return Number(item[column.property]) || 0;
				});
				const sum = values?.reduce((prev, curr) => {
					return !isNaN(Number(curr)) ? bigAdd(prev, curr, 1) : prev;
				}, 0); //合计计算
				means[columnIndex] = sum > 0 ? <span class="pr10 green">{sum}</span> : '';
			}
			return [means];
		},
		// 查询
		queryByInput(queryStr, cb, type, row) {
			if (!queryStr) {
				this.handleSelect({}, row, type);
			}

			const API_MAP = {
				客户: {
					API: 'selectInquiryDocumentaryList',
					DATA: JSON.stringify({
						channelName: [],
						queryParam: queryStr,
						// businessOpportunityQuality: [0, 1, 2, 3, 4],
						// quality: [0, 1, 2, 3, 4],
						// stage: [1, 2, 3, 4, 5, 6, 8, 9],
						// rePurchase: 0,
						pageNum: 1,
						pageSize: 100,
					}),
				},
				合同: {
					API: 'selectDeliverManagement',
					DATA: JSON.stringify({
						twid: [],
						channelName: [],
						contractFlag: ['0', '1'],
						query: queryStr,
						queryParam: this.nowDetailForm.registeredBusinessName || '',
						flag: 1,
						pageNum: 1,
						pageSize: 100,
					}),
				},
			};
			this.$axios[API_MAP[type].API](API_MAP[type].DATA)
				.then(res => {
					if (res.data.success) {
						cb(res.data.data);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API_MAP[type].API} |` + error);
				});
		},
		// 选择
		handleSelect(item, type, row) {
			if (type == '客户') {
				this.nowDetailForm.idid = item?.idid || '';
				this.nowDetailForm.registeredBusinessName = item?.registeredBusinessName || item?.companyName || '';
			} else if (type == '合同') {
				this.nowDetailForm.contractNo = item?.contractNo || '';
				this.nowDetailForm.dmid = item?.dmid || '';

				this.nowDetailForm.idid = item?.idid || '';
				this.nowDetailForm.registeredBusinessName = item?.registeredBusinessName || item?.companyName || '';
			}
		},
		// 添加一行
		addRow(row, index) {
			if (row) {
				this.nowTableData.splice(index + 1, 0, {});
			} else {
				this.nowTableData.push({});
			}
			this.$refs.uTableRef.scrollBottom();
		},
		// 复制
		async copyRow(row, index) {
			if (row) {
				this.nowTableData.splice(index + 1, 0, { ...row });
			} else {
				this.nowTableData.push({});
			}
			this.$refs.uTableRef.scrollBottom();
		},

		// 删除
		async delRow(row, index) {
			this.nowTableData.splice(index, 1);
		},
		// 保存
		async save() {
			const API = this.nowDetailForm.dsid ? 'updateDemandSupervisionDetailByDsid' : 'addDemandSuperVision';
			const updateDemandSuperVisionDetailDTOS = this.nowTableData;
			const addDemandSuperVisionDetailDTOS = this.nowTableData;
			try {
				const res = await this.$axios[API](
					JSON.stringify({
						// ckrid: this.nowDetailForm.ckrid,
						demandDocumentName: this.detailForm.demandDocumentName,
						demandDocumentUrl: this.detailForm.demandDocumentUrl,
						dmid: this.nowDetailForm.dmid,
						dsid: this.nowDetailForm.dsid,
						idid: this.nowDetailForm.idid,
						projectCategory: this.nowDetailForm.projectCategory,
						registeredBusinessName: this.nowDetailForm.registeredBusinessName,
						updateDemandSuperVisionDetailDTOS,
						addDemandSuperVisionDetailDTOS,
					}),
				);
				if (res.data.success) {
					if (!this.nowDetailForm.dsid) {
						this.nowDetailForm.dsid = res.data.data.dsid;
					}
					this.$emit('refresh', this.nowDetailForm);
					this.nowTableDataCopy = deepClone(this.nowTableData);
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 提交
		async submit() {
			if (this.isUpdate) {
				return this.$message.warning('请先保存需求信息!');
			}

			if (!this.nowDetailForm.dsid) return;
			const API = 'submitDemandSuperVision';
			try {
				const res = await this.$axios[API](JSON.stringify({ id: this.nowDetailForm.dsid }));
				if (res.data.success) {
					this.$emit('close');
					this.clear();
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		// 清空
		async clear() {
			this.nowDetailForm = resetValues(this.detailForm);
			this.nowTableData = [];
			this.nowTableDataCopy = [];
		},

		dateFormat, // 日期格式化
		jointString, // 拼接字符串
	},
};
</script>

<style lang="scss" scoped>
.DemandTableBase {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
	color: #666;

	.detail-table {
		height: auto !important;
		min-height: fit-content;
		td {
			height: 30px !important;
		}
	}
	.el-table__body-wrapper,
	.el-table__footer-wrapper {
		td,
		.cell {
			padding: 0 !important;
		}
	}

	.table-wrapper .table-main td .cell {
		padding: 0 !important;
	}
}
</style>
